<template>
  <div class="theme-test-page">
    <h1>🎨 主题测试页面</h1>
    <p>测试 setTheme('#722ed1') 对不同组件的效果</p>

    <!-- 主题切换按钮 -->
    <div class="theme-controls">
      <button
        @click="switchTheme('#722ed1')"
        class="test-btn"
      >
        设置紫色主题 (#722ed1)
      </button>
      <button
        @click="switchTheme('#1890ff')"
        class="test-btn"
      >
        设置蓝色主题 (#1890ff)
      </button>
      <button
        @click="switchTheme('#52c41a')"
        class="test-btn"
      >
        设置绿色主题 (#52c41a)
      </button>
      <button
        @click="switchTheme('#ff6b6b')"
        class="test-btn"
      >
        设置红色主题 (#ff6b6b)
      </button>
      <button
        @click="checkCSSVariables"
        class="test-btn"
      >
        检查 CSS 变量
      </button>
    </div>

    <!-- CSS 变量显示 -->
    <div class="css-variables">
      <h3>当前 CSS 变量值：</h3>
      <div class="variable-list">
        <div
          v-for="(value, key) in cssVariables"
          :key="key"
          class="variable-item"
        >
          <span class="variable-name">{{ key }}:</span>
          <span
            class="variable-value"
            :style="{ color: value }"
          >
            {{ value }}
          </span>
          <div
            class="color-preview"
            :style="{ backgroundColor: value }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 组件测试区域 -->
    <div class="component-tests">
      <h3>组件效果测试：</h3>

      <!-- 按钮测试 -->
      <div class="test-section">
        <h4>🔘 按钮组件 (Btn)</h4>
        <div class="component-row">
          <Btn variant="default">默认按钮</Btn>
          <Btn variant="outlined">轮廓按钮</Btn>
          <Btn variant="text">文本按钮</Btn>
          <Btn variant="underline">下划线按钮</Btn>
        </div>
      </div>

      <!-- 单选按钮测试 -->
      <div class="test-section">
        <h4>📻 单选按钮组件 (Radio)</h4>
        <div class="component-row">
          <SpRadio
            v-model="radioValue"
            value="option1"
            label="选项 1"
          />
          <SpRadio
            v-model="radioValue"
            value="option2"
            label="选项 2"
          />
          <SpRadio
            v-model="radioValue"
            value="option3"
            label="选项 3"
          />
        </div>
        <p>当前选择: {{ radioValue }}</p>
      </div>

      <!-- 输入框测试 -->
      <div class="test-section">
        <h4>📝 输入框组件 (InputField)</h4>
        <div class="component-row">
          <SpInputField
            v-model="inputValue"
            label="测试输入框"
            placeholder="请输入内容"
            style="width: 200px"
          />
        </div>
        <p>输入值: {{ inputValue }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { setTheme } from '../../../packages/ui/src'
  import Btn from '../../../packages/ui/src/components/btn/btn'
  import SpRadio from '../../../packages/ui/src/components/radio/radio'
  import SpInputField from '../../../packages/ui/src/components/input-field/input-field.vue'

  // 响应式数据
  const radioValue = ref('option1')
  const inputValue = ref('')
  const cssVariables = ref<Record<string, string>>({})

  // CSS 变量名列表
  const variableNames = [
    '--sp-color-primary',
    '--sp-color-primary-hover',
    '--sp-color-primary-active',
    '--sp-color-primary-disabled',
    '--sp-color-primary-lightest',
    '--sp-color-primary-light',
  ]

  // 检查 CSS 变量
  const checkCSSVariables = () => {
    const root = document.documentElement
    const computedStyle = getComputedStyle(root)

    const variables: Record<string, string> = {}
    variableNames.forEach(name => {
      variables[name] = computedStyle.getPropertyValue(name).trim()
    })

    cssVariables.value = variables
    console.log('🔍 当前 CSS 变量:', variables)
  }

  // 主题切换函数
  const switchTheme = (color: string) => {
    console.log(`🎨 切换主题到: ${color}`)
    setTheme(color)

    // 延迟检查变量，确保更新完成
    setTimeout(() => {
      checkCSSVariables()
    }, 50)
  }

  // 组件挂载时检查初始状态
  onMounted(() => {
    console.log('📋 页面加载完成，检查初始 CSS 变量')
    checkCSSVariables()
  })
</script>

<style scoped>
  .theme-test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .theme-controls {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .test-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f5f5f5;
    cursor: pointer;
    transition: all 0.2s;
  }

  .test-btn:hover {
    background: #e0e0e0;
    border-color: #bbb;
  }

  .css-variables {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .variable-list {
    display: grid;
    gap: 8px;
  }

  .variable-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 5px;
    background: white;
    border-radius: 4px;
  }

  .variable-name {
    font-family: monospace;
    font-weight: bold;
    min-width: 200px;
  }

  .variable-value {
    font-family: monospace;
    min-width: 100px;
  }

  .color-preview {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    border: 1px solid #ccc;
  }

  .component-tests {
    margin: 30px 0;
  }

  .test-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  .component-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    margin: 10px 0;
  }

  h1,
  h3,
  h4 {
    color: #333;
  }

  h4 {
    margin-bottom: 10px;
  }
</style>
