<template>
  <div class="checkbox-demo">
    <h1>SpCheckbox 复选框演示</h1>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>🎯 基础用法</h2>
      <div class="demo-row">
        <SpCheckbox
          v-model="basicValue1"
          label="选项 1"
        />
        <SpCheckbox
          v-model="basicValue2"
          label="选项 2"
        />
        <SpCheckbox
          v-model="basicValue3"
          label="选项 3"
        />
      </div>
      <p class="demo-info">当前选择: {{ { basicValue1, basicValue2, basicValue3 } }}</p>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-row">
        <div class="size-group">
          <h3>小尺寸</h3>
          <SpCheckbox
            v-model="sizeValue"
            size="small"
            label="小尺寸复选框"
          />
        </div>

        <div class="size-group">
          <h3>中等尺寸（默认）</h3>
          <SpCheckbox
            v-model="sizeValue"
            size="medium"
            label="中等尺寸复选框"
          />
        </div>

        <div class="size-group">
          <h3>大尺寸</h3>
          <SpCheckbox
            v-model="sizeValue"
            size="large"
            label="大尺寸复选框"
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ sizeValue }}</p>
    </section>

    <!-- 标签位置 -->
    <section class="demo-section">
      <h2>🏷️ 标签位置</h2>
      <div class="demo-row">
        <div class="label-group">
          <h3>标签在右侧（默认）</h3>
          <SpCheckbox
            v-model="labelValue"
            label="右侧标签"
            label-position="right"
          />
        </div>

        <div class="label-group">
          <h3>标签在左侧</h3>
          <SpCheckbox
            v-model="labelValue"
            label="左侧标签"
            label-position="left"
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ labelValue }}</p>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🔒 状态演示</h2>
      <div class="demo-row">
        <div class="state-group">
          <h3>正常状态</h3>
          <SpCheckbox
            v-model="stateValue1"
            label="正常复选框"
          />
        </div>

        <div class="state-group">
          <h3>禁用状态</h3>
          <SpCheckbox
            v-model="stateValue2"
            label="禁用复选框"
            disabled
          />
        </div>

        <div class="state-group">
          <h3>不确定状态</h3>
          <SpCheckbox
            v-model="stateValue3"
            label="不确定状态"
            indeterminate
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ { stateValue1, stateValue2, stateValue3 } }}</p>
    </section>

    <!-- 数组绑定 -->
    <section class="demo-section">
      <h2>📋 数组绑定</h2>
      <div class="demo-row">
        <SpCheckbox
          v-model="arrayValue"
          value="apple"
          label="苹果"
        />
        <SpCheckbox
          v-model="arrayValue"
          value="banana"
          label="香蕉"
        />
        <SpCheckbox
          v-model="arrayValue"
          value="orange"
          label="橙子"
        />
        <SpCheckbox
          v-model="arrayValue"
          value="grape"
          label="葡萄"
        />
      </div>
      <p class="demo-info">选中的水果: {{ arrayValue }}</p>
    </section>

    <!-- 变体演示 -->
    <section class="demo-section">
      <h2>🎨 变体演示</h2>
      <div class="demo-row">
        <div class="variant-group">
          <h3>默认变体</h3>
          <SpCheckbox
            v-model="variantValue1"
            variant="default"
            label="默认样式"
          />
          <SpCheckbox
            v-model="variantValue2"
            variant="default"
            label="默认样式 - 选中"
          />
        </div>

        <div class="variant-group">
          <h3>带边框变体</h3>
          <SpCheckbox
            v-model="variantValue3"
            variant="outlined"
            size="small"
            label="2"
          />
          <SpCheckbox
            v-model="variantValue4"
            variant="outlined"
            label="Option2"
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ { variantValue1, variantValue2, variantValue3, variantValue4 } }}</p>
    </section>

    <!-- 使用插槽 -->
    <section class="demo-section">
      <h2>🎪 使用插槽</h2>
      <div class="demo-row">
        <SpCheckbox
          v-model="slotValue1"
          :show-label="false"
        >
          <span style="color: #1890ff; font-weight: bold">🚀 自定义内容 1</span>
        </SpCheckbox>

        <SpCheckbox
          v-model="slotValue2"
          :show-label="false"
        >
          <div style="display: flex; align-items: center; gap: 8px">
            <span
              style="
                background: #f0f0f0;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
              "
            >
              NEW
            </span>
            <span>带标签的选项</span>
          </div>
        </SpCheckbox>

        <SpCheckbox
          v-model="slotValue3"
          :show-label="false"
        >
          <div
            style="
              border: 1px solid #d9d9d9;
              padding: 8px;
              border-radius: 4px;
              background: #fafafa;
            "
          >
            <div style="font-weight: bold">复杂选项</div>
            <div style="font-size: 12px; color: #666">包含描述信息的选项</div>
          </div>
        </SpCheckbox>
      </div>
      <p class="demo-info">当前选择: {{ { slotValue1, slotValue2, slotValue3 } }}</p>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SpCheckbox from '../checkbox'

// 响应式数据
const basicValue1 = ref(true)
const basicValue2 = ref(false)
const basicValue3 = ref(true)
const sizeValue = ref(false)
const labelValue = ref(false)
const stateValue1 = ref(false)
const stateValue2 = ref(true) // 预选中禁用项
const stateValue3 = ref(false)
const arrayValue = ref(['apple', 'orange'])
const variantValue1 = ref(false)
const variantValue2 = ref(true)
const variantValue3 = ref(false)
const variantValue4 = ref(true)
const slotValue1 = ref(true)
const slotValue2 = ref(false)
const slotValue3 = ref(true)
</script>

<style scoped>
.checkbox-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
}

.demo-section h3 {
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.demo-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.size-group,
.label-group,
.state-group,
.variant-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 200px;
}

.demo-info {
  margin: 10px 0;
  padding: 10px;
  background: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  color: #0958d9;
}
</style>
