<template>
  <div class="sp-switch-wrapper">
    <!-- 外部文本 -->
    <span 
      v-if="props.offOutText" 
      :class="['sp-switch__out-text', 'sp-switch__out-text--off', { 'sp-switch__out-text--visible': !checked }]"
    >
      {{ props.offOutText }}
    </span>
    
    <div
      :class="[
        'sp-switch',
        `sp-switch--${props.size}`,
        {
          'sp-switch--checked': checked,
          'sp-switch--disabled': disabled,
          'sp-switch--square': square,
          'sp-switch--vertical': vertical
        }
      ]"
      @click="handleClick"
    >
    <span class="sp-switch__core" :style="coreStyle">
      <span
        v-if="onText || onIcon"
        :class="['sp-switch__text', 'sp-switch__text--on', { 'sp-switch__text--visible': checked }]"
      >
        <component v-if="onIcon" :is="onIcon" class="sp-switch__icon" />
        <span v-if="onText" class="sp-switch__text-content">{{ onText }}</span>
      </span>
      <span
        v-if="offText || offIcon"
        :class="['sp-switch__text', 'sp-switch__text--off', { 'sp-switch__text--visible': !checked }]"
      >
        <component v-if="offIcon" :is="offIcon" class="sp-switch__icon" />
        <span v-if="offText" class="sp-switch__text-content">{{ offText }}</span>
      </span>
      <span class="sp-switch__button" :style="buttonStyle">
        <span 
          v-if="isCountingDown" 
          class="sp-switch__countdown"
        >
          {{ countdown }}
        </span>
        <component 
          v-else-if="props.buttonIcon" 
          :is="props.buttonIcon" 
          :class="['sp-switch__button-icon', { 'sp-switch__button-icon--loading': props.loading }]"
        />
        <div 
          v-else-if="props.loading" 
          class="sp-switch__loading-icon"
        >
          <!-- Loading spinner content -->
        </div>
      </span>
    </span>
    <span v-if="$slots.default" class="sp-switch__label">
      <slot></slot>
    </span>
    </div>
    
    <!-- 开启状态的外部文本 -->
    <span 
      v-if="props.onOutText" 
      :class="['sp-switch__out-text', 'sp-switch__out-text--on', { 'sp-switch__out-text--visible': checked }]"
    >
      {{ props.onOutText }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'

// Props 验证优化
const props = defineProps({
  value: { type: Boolean, default: undefined },
  disabled: { type: Boolean, default: false },
  size: { 
    type: String, 
    default: 'medium',
    validator: (value: string) => ['small', 'medium', 'large', 'huge'].includes(value)
  },
  activeColor: { type: String, default: '' },
  inactiveColor: { type: String, default: '' },
  onText: { type: String, default: '' },
  offText: { type: String, default: '' },
  onOutText: { type: String, default: '' },
  offOutText: { type: String, default: '' },
  onIcon: { type: Object, default: null },
  offIcon: { type: Object, default: null },
  buttonIcon: { type: Object, default: null },
  loading: { type: Boolean, default: false },
  square: { type: Boolean, default: false },
  vertical: { type: Boolean, default: false },
  time: {
    type: Number,
    default: undefined,
    validator: (value: number) => value === undefined || value > 0
  },
  switchColor: { type: String, default: '' },
  buttonColor: { type: String, default: '' },
  switchOnColor: { type: String, default: '' },
  switchOffColor: { type: String, default: '' },
  buttonOnColor: { type: String, default: '' },
  buttonOffColor: { type: String, default: '' }
})

const emit = defineEmits(['update:value', 'change', 'countdown-end'])

const innerValue = ref(props.value ?? false)
watch(() => props.value, (val) => {
  if (val !== undefined) innerValue.value = val
})
const checked = computed(() => innerValue.value)

// 文字宽度缓存（带大小限制）
const textWidthCache = new Map<string, number>()
const MAX_CACHE_SIZE = 100

// 清理缓存函数
const clearCacheIfNeeded = () => {
  if (textWidthCache.size > MAX_CACHE_SIZE) {
    // 清理一半的缓存项
    const entries = Array.from(textWidthCache.entries())
    textWidthCache.clear()
    // 保留最近使用的一半
    entries.slice(-Math.floor(MAX_CACHE_SIZE / 2)).forEach(([key, value]) => {
      textWidthCache.set(key, value)
    })
  }
}

// 倒计时相关状态
const countdown = ref(0)
const isCountingDown = ref(false)
let countdownTimer: number | null = null

// 初始化倒计时
const initCountdown = () => {
  if (props.time && props.time > 0) {
    countdown.value = props.time
    isCountingDown.value = true
    startCountdown()
  }
}

// 开始倒计时 - 优化版本
const startCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  
  let lastTime = Date.now()
  
  countdownTimer = window.setInterval(() => {
    const now = Date.now()
    // 确保时间间隔准确
    if (now - lastTime >= 1000) {
      if (countdown.value > 0) {
        countdown.value--
        lastTime = now
      } else {
        isCountingDown.value = false
        clearInterval(countdownTimer!)
        countdownTimer = null
        emit('countdown-end')
      }
    }
  }, 100) // 更频繁检查但更精确
}

// 监听 time 属性变化
watch(() => props.time, (newTime) => {
  if (newTime && newTime > 0) {
    countdown.value = newTime
    isCountingDown.value = true
    startCountdown()
  } else {
    isCountingDown.value = false
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }
})

// 组件挂载时初始化倒计时
onMounted(() => {
  initCountdown()
})

// 组件卸载时清理定时器和缓存
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  // 清理缓存
  textWidthCache.clear()
})

// 移除 JS 控制的按钮尺寸，改为纯 CSS 实现

// 优化的文字宽度计算函数（带缓存和清理机制）
const getTextWidth = (text: string): number => {
  if (!text) return 0
  
  if (textWidthCache.has(text)) {
    return textWidthCache.get(text)!
  }
  
  let width = 0
  for (let i = 0; i < text.length; i++) {
    const char = text[i]
    if (/[\u4e00-\u9fa5]/.test(char)) {
      width += 14 // 中文字符
    } else if (/[0-9]/.test(char)) {
      width += 7 // 数字
    } else {
      width += 8 // 英文字符
    }
  }
  
  clearCacheIfNeeded()
  textWidthCache.set(text, width)
  return width
}

// 计算内容宽度（图标 + 文字）
const getContentWidth = (text: string, hasIcon: boolean): number => {
  let width = 0
  if (hasIcon) {
    width += 16 // 图标宽度
    if (text) width += 4 // 图标和文字间距
  }
  if (text) {
    width += getTextWidth(text)
  }
  return width
}

// 尺寸配置
const sizeConfig = computed(() => {
  switch (props.size) {
    case 'small':
      return { minWidth: 30, buttonWidth: 14, padding: 3 }
    case 'large':
      return { minWidth: 60, buttonWidth: 30, padding: 5 }
    case 'huge':
      return { minWidth: 70, buttonWidth: 34, padding: 5 }
    default: // medium
      return { minWidth: 50, buttonWidth: 22, padding: 5 }
  }
})

// 当前内容宽度
const currentContentWidth = computed(() => {
  const currentText = checked.value ? (props.onText || '') : (props.offText || '')
  const currentIcon = checked.value ? props.onIcon : props.offIcon
  return getContentWidth(currentText, !!currentIcon)
})

// 开关宽度
const switchWidth = computed(() => {
  if (props.vertical) return undefined
  return Math.max(
    sizeConfig.value.minWidth, 
    currentContentWidth.value + sizeConfig.value.buttonWidth + sizeConfig.value.padding + 16
  )
})

// 开关背景颜色
const switchBackgroundColor = computed(() => {
  return checked.value
    ? (props.switchOnColor || props.switchColor || props.activeColor || '#6c8cff')
    : (props.switchOffColor || props.switchColor || props.inactiveColor || '#e5e6eb')
})

// 主样式计算属性（拆分后）
const coreStyle = computed(() => {
  if (props.vertical) {
    return {}
  }

  return {
    width: `${switchWidth.value}px`,
    height: '26px',
    background: switchBackgroundColor.value,
    borderRadius: '16px',
    position: 'relative' as const,
    display: 'inline-block',
    transition: 'background 0.3s, width 0.3s ease'
  }
})

// 按钮背景颜色
const buttonBackgroundColor = computed(() => {
  return checked.value
    ? (props.buttonOnColor || props.buttonColor)
    : (props.buttonOffColor || props.buttonColor)
})

// 按钮样式计算属性（优化后）
const buttonStyle = computed(() => {
  return {
    backgroundColor: buttonBackgroundColor.value || undefined
  }
})

// 移除 JS 控制的文字样式，改为纯 CSS 实现

const handleClick = () => {
  if (props.disabled) return
  const newValue = !innerValue.value
  emit('update:value', newValue)
  emit('change', newValue)
  if (props.value === undefined) {
    innerValue.value = newValue
  }
}
</script>

<style scoped>
.sp-switch__core {
  position: relative;
  overflow: hidden;
  vertical-align: middle;
}
.sp-switch__button {
  cursor: pointer;
  box-sizing: border-box;
  border: none;
  outline: none;
  z-index: 2;
}
.sp-switch__text {
  z-index: 1;
  user-select: none;
}
.sp-switch__loading-icon {
  width: 12px;
  height: 12px;
  border: 2px solid #ccc;
  border-top: 2px solid #6c8cff;
  border-radius: 50%;
  animation: sp-switch-loading 1s linear infinite;
}
</style>