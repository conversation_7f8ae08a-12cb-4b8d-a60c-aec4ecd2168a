<template>
  <div class="control-demo">
    <h1>SpControl 通用控件系统演示</h1>
    
    <!-- 基础 SpControl 演示 -->
    <section class="demo-section">
      <h2>🎯 基础 SpControl</h2>
      <div class="demo-row">
        <SpControl
          v-model="basicValue"
          variant="primary"
          @click="handleBasicClick"
        >
          基础控件
        </SpControl>
        
        <SpControl
          variant="secondary"
          disabled
        >
          禁用控件
        </SpControl>
        
        <SpControl
          variant="success"
          loading
        >
          加载中
        </SpControl>
        
        <SpControl
          variant="error"
          error
        >
          错误状态
        </SpControl>
      </div>
    </section>

    <!-- SpButton 演示 -->
    <section class="demo-section">
      <h2>🔘 SpButton 按钮</h2>
      
      <div class="demo-row">
        <h3>基础按钮</h3>
        <SpButton
          variant="default"
          @click="handleButtonClick('default')"
        >
          默认按钮
        </SpButton>
        
        <SpButton
          variant="primary"
          @click="handleButtonClick('primary')"
        >
          主要按钮
        </SpButton>
        
        <SpButton
          variant="secondary"
          @click="handleButtonClick('secondary')"
        >
          次要按钮
        </SpButton>
        
        <SpButton
          variant="success"
          @click="handleButtonClick('success')"
        >
          成功按钮
        </SpButton>
      </div>

      <div class="demo-row">
        <h3>按钮尺寸</h3>
        <SpButton size="small">小按钮</SpButton>
        <SpButton size="medium">中按钮</SpButton>
        <SpButton size="large">大按钮</SpButton>
      </div>

      <div class="demo-row">
        <h3>图标按钮</h3>
        <SpButton
          icon="search"
          icon-position="left"
        >
          搜索
        </SpButton>
        
        <SpButton
          icon="download"
          icon-position="right"
        >
          下载
        </SpButton>
        
        <SpButton
          circle
          icon="plus"
          variant="primary"
        />
        
        <SpButton
          circle
          icon="heart"
          variant="error"
        />
      </div>

      <div class="demo-row">
        <h3>按钮状态</h3>
        <SpButton disabled>禁用按钮</SpButton>
        
        <SpButton
          :loading="buttonLoading"
          @click="toggleButtonLoading"
        >
          {{ buttonLoading ? '加载中...' : '点击加载' }}
        </SpButton>
        
        <SpButton block variant="primary">
          块级按钮
        </SpButton>
      </div>
    </section>

    <!-- SpSwitch 演示 -->
    <section class="demo-section">
      <h2>🔄 SpSwitch 开关</h2>
      
      <div class="demo-row">
        <h3>基础开关</h3>
        <SpSwitch
          v-model="switchValue1"
          @change="handleSwitchChange"
        />
        
        <SpSwitch
          v-model="switchValue2"
          checked-color="#52c41a"
          unchecked-color="#d9d9d9"
        />
        
        <SpSwitch
          disabled
          :model-value="true"
        />
      </div>

      <div class="demo-row">
        <h3>带文本的开关</h3>
        <SpSwitch
          v-model="switchValue3"
          show-text
          checked-text="开启"
          unchecked-text="关闭"
        />
        
        <SpSwitch
          v-model="switchValue4"
          show-text
          checked-text="ON"
          unchecked-text="OFF"
          size="large"
        />
      </div>

      <div class="demo-row">
        <h3>带图标的开关</h3>
        <SpSwitch
          v-model="switchValue5"
          show-icon
          checked-icon="check"
          unchecked-icon="close"
        />
        
        <SpSwitch
          v-model="switchValue6"
          show-icon
          show-text
          checked-icon="sun"
          unchecked-icon="moon"
          checked-text="日间"
          unchecked-text="夜间"
        />
      </div>

      <div class="demo-row">
        <h3>开关尺寸</h3>
        <SpSwitch
          v-model="switchValue7"
          size="small"
        />
        
        <SpSwitch
          v-model="switchValue8"
          size="medium"
        />
        
        <SpSwitch
          v-model="switchValue9"
          size="large"
        />
      </div>
    </section>

    <!-- 自定义控件演示 -->
    <section class="demo-section">
      <h2>🎨 自定义控件</h2>
      <div class="demo-row">
        <SpControl
          v-model="customValue"
          variant="primary"
          :rounded="8"
        >
          <template #default="{ state, behavior, styles }">
            <div
              :class="['custom-control', styles.classes.value]"
              :style="styles.styles.value"
              @click="behavior.updateValue(!state.value)"
            >
              <span>{{ state.value ? '✓' : '○' }}</span>
              <span>自定义控件</span>
            </div>
          </template>
        </SpControl>
      </div>
    </section>

    <!-- 状态显示 -->
    <section class="demo-section">
      <h2>📊 状态显示</h2>
      <div class="demo-info">
        <p><strong>基础值:</strong> {{ basicValue }}</p>
        <p><strong>开关状态:</strong> {{ switchStates }}</p>
        <p><strong>自定义值:</strong> {{ customValue }}</p>
        <p><strong>按钮加载:</strong> {{ buttonLoading }}</p>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { SpControl, SpButton, SpSwitch } from '../index'

// 响应式数据
const basicValue = ref(false)
const customValue = ref(false)
const buttonLoading = ref(false)

// 开关状态
const switchValue1 = ref(false)
const switchValue2 = ref(true)
const switchValue3 = ref(false)
const switchValue4 = ref(true)
const switchValue5 = ref(false)
const switchValue6 = ref(false)
const switchValue7 = ref(false)
const switchValue8 = ref(true)
const switchValue9 = ref(false)

// 计算属性
const switchStates = computed(() => ({
  switch1: switchValue1.value,
  switch2: switchValue2.value,
  switch3: switchValue3.value,
  switch4: switchValue4.value,
  switch5: switchValue5.value,
  switch6: switchValue6.value,
  switch7: switchValue7.value,
  switch8: switchValue8.value,
  switch9: switchValue9.value,
}))

// 事件处理
const handleBasicClick = () => {
  console.log('基础控件被点击')
}

const handleButtonClick = (type: string) => {
  console.log(`${type} 按钮被点击`)
}

const handleSwitchChange = (value: any) => {
  console.log('开关状态改变:', value)
}

const toggleButtonLoading = () => {
  buttonLoading.value = true
  setTimeout(() => {
    buttonLoading.value = false
  }, 2000)
}
</script>

<style scoped>
.control-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
}

.demo-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #1890ff;
}

.demo-row {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.demo-row h3 {
  width: 100%;
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #666;
}

.demo-info {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  font-family: monospace;
}

.demo-info p {
  margin: 8px 0;
}

.custom-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 2px solid currentColor;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
}

.custom-control:hover {
  transform: scale(1.05);
}
</style>
