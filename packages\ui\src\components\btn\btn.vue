<template>
  <BtnBase
    :class="buttonClasses"
    :style="buttonStyles"
    :disabled="isDisabled"
    ref="btnBaseRef"
    v-bind="$attrs"
  >
    <!-- 前置插槽 -->
    <template v-if="$slots.prepend || $slots['prepend-icon']">
      <span :class="bem.e('prepend')">
        <slot name="prepend">
          <slot name="prepend-icon" />
        </slot>
      </span>
    </template>

    <!-- 主要内容 -->
    <span
      v-if="$slots.default"
      :class="bem.e('content')"
    >
      <slot />
    </span>

    <!-- 后置插槽 -->
    <template v-if="$slots.append || $slots['append-icon']">
      <span :class="bem.e('append')">
        <slot name="append">
          <slot name="append-icon" />
        </slot>
      </span>
    </template>
  </BtnBase>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import BtnBase from './btn-base.vue'
  import { useBtnState } from '../../composables/useBtnState'
  import {
    provideBtnContext,
    createBtnContext,
  } from '../../composables/useBtnContext'

  // 样式层组件，负责样式计算和类名生成
  interface Props {
    variant?: 'default' | 'outlined' | 'text'
    disabled?: boolean
    size?: 'small' | 'medium' | 'large'
    isHovered?: boolean
    isPressed?: boolean
  }

  const props = defineProps<Props>()

  // 确保 disabled 有明确的值
  const isDisabled = computed(() => props.disabled === true)

  // 获取逻辑层组件的引用（保持向后兼容）
  const btnBaseRef = ref()

  // 使用按钮状态管理 composable
  const {
    buttonClasses,
    buttonStyles,
    currentIsHovered,
    currentIsPressed,
    bem,
  } = useBtnState({
    props: computed(() => ({
      variant: props.variant || 'default',
      disabled: isDisabled.value,
      size: props.size || 'medium',
      isHovered: props.isHovered || false,
      isPressed: props.isPressed || false,
    })),
    btnBaseRef,
  })

  // 创建并提供按钮上下文
  const btnContext = createBtnContext({
    isHovered: currentIsHovered,
    isPressed: currentIsPressed,
    disabled: isDisabled,
    variant: props.variant || 'default',
    size: props.size || 'medium',
  })

  provideBtnContext(btnContext)
</script>
