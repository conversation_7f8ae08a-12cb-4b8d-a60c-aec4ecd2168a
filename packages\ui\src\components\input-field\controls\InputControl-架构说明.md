# InputControl 控件系统架构说明

## 🎯 组件概述

`InputControl` 控件系统是 Speed UI 中所有输入框控件的统一管理架构，它为 `ClearControl`、`PasswordControl`、`NumberControl`、`SearchControl` 等组件提供统一的注册、渲染和事件处理机制。

## 🏗️ 设计模式

### 1. 控件注册模式 (Control Registry Pattern)

- **ControlRegistry** 作为控件注册中心，管理所有控件配置
- **ControlConfig** 定义控件的标准配置格式
- **动态注册** 支持运行时注册和注销控件

### 2. 渲染器模式 (Renderer Pattern)

- **ControlRenderer** 作为统一渲染器，根据配置动态渲染控件
- **位置感知** 支持 prepend、append、prependOuter、appendOuter 四种位置
- **条件渲染** 基于上下文条件动态显示/隐藏控件

### 3. 工厂模式 (Factory Pattern)

- **propsFactory** 根据上下文动态生成控件属性
- **eventsFactory** 根据上下文动态生成事件处理器
- **contextFactory** 创建统一的控件上下文

## 🔧 核心功能

### 1. 控件配置系统

```typescript
// 标准控件配置格式
interface ControlConfig {
  type: ControlType // 控件类型标识
  component: Component // Vue 组件
  position: ControlPosition // 渲染位置
  order: number // 显示顺序
  condition: (context) => boolean // 显示条件
  propsFactory: (context) => Props // 属性工厂
  eventsFactory: (context) => Events // 事件工厂
}
```

### 2. 动态渲染系统

```typescript
// 根据位置和条件动态渲染控件
const renderedControls = computed(() => {
  return getControlsByPosition(position)
    .filter(config => config.condition(context))
    .map(config => ({
      component: config.component,
      props: config.propsFactory(context),
      events: config.eventsFactory(context),
      order: config.order,
    }))
    .sort((a, b) => a.order - b.order)
})
```

### 3. 统一事件处理

```typescript
// 事件包装器，提供统一的事件通知机制
const wrappedEvents = Object.entries(controlEvents).map(([name, handler]) => ({
  [name]: (...args) => {
    const result = handler(...args)
    emit('control-event', { type, event: name, data: args[0] })
    return result
  },
}))
```

## 🎨 控件系统架构

### 1. 核心组件层次

```
InputField (主组件)
├── FieldContainer (容器组件)
│   ├── prepend 插槽
│   │   └── ControlRenderer (position="prepend")
│   ├── input 区域
│   ├── append 插槽
│   │   └── ControlRenderer (position="append")
│   ├── prependOuter 插槽
│   │   └── ControlRenderer (position="prependOuter")
│   └── appendOuter 插槽
│       └── ControlRenderer (position="appendOuter")
└── useInputControls (控件逻辑)
```

### 2. 控件配置流程

```
1. 控件注册 → ControlRegistry
2. 配置验证 → ControlConfig
3. 条件检查 → condition(context)
4. 属性生成 → propsFactory(context)
5. 事件绑定 → eventsFactory(context)
6. 动态渲染 → ControlRenderer
7. 事件处理 → 统一事件系统
```

## 📋 内置控件实现

### 1. ClearControl (清除控件)

```typescript
const clearControlConfig: ControlConfig = {
  type: 'clear',
  component: ClearControl,
  position: 'append',
  order: 100,
  condition: context => context.inputLogic.showClearIcon.value,
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize,
    disabled: context.computed.computedDisabled,
  }),
  eventsFactory: context => ({
    click: () => context.inputLogic.clearValue(),
  }),
}
```

### 2. PasswordControl (密码控件)

```typescript
const passwordControlConfig: ControlConfig = {
  type: 'password',
  component: PasswordControl,
  position: 'append',
  order: 90,
  condition: context => context.props.type === 'password',
  propsFactory: context => ({
    visible: context.inputLogic.passwordVisible.value,
    iconSize: context.slotProps.iconSize,
  }),
  eventsFactory: context => ({
    toggle: () => context.inputLogic.togglePasswordVisibility(),
  }),
}
```

## 🔄 控件生命周期

### 1. 注册阶段

```typescript
// 1. 创建控件配置
const config = createControlConfig(...)

// 2. 注册到系统
registerControl(config)

// 3. 验证配置有效性
validateControlConfig(config)
```

### 2. 渲染阶段

```typescript
// 1. 获取位置相关控件
const configs = getControlsByPosition(position)

// 2. 过滤显示条件
const visibleConfigs = configs.filter(config => config.condition(context))

// 3. 生成渲染数据
const renderedControls = visibleConfigs.map(config => ({
  component: config.component,
  props: config.propsFactory(context),
  events: config.eventsFactory(context),
}))

// 4. 按顺序渲染
renderedControls.sort((a, b) => a.order - b.order)
```

### 3. 交互阶段

```typescript
// 1. 用户交互触发事件
userInteraction() → controlEvent

// 2. 事件处理器执行
eventHandler(eventData)

// 3. 发送控件事件通知
emit('control-event', { type, event, data })

// 4. 更新组件状态
updateComponentState()
```

## 🎯 设计优势

### 1. 相比传统硬编码方式

**传统方式 (硬编码)**:

```vue
<template>
  <!-- 每个控件都需要单独处理 -->
  <sp-icon
    v-if="showClearIcon"
    @click="clearValue"
  />
  <sp-icon
    v-if="showPasswordIcon"
    @click="togglePassword"
  />
  <number-controls
    v-if="showNumberControls"
    @change="handleNumber"
  />
  <search-controls
    v-if="showSearchControls"
    @search="handleSearch"
  />
  <!-- ... 更多硬编码控件 -->
</template>
```

**控件系统方式**:

```vue
<template>
  <!-- 统一的控件渲染 -->
  <ControlRenderer
    :position="position"
    :context="controlContext"
    @control-event="handleControlEvent"
  />
</template>
```

### 2. 核心改进

1. **代码复用**: 统一的渲染逻辑，减少重复代码
2. **动态扩展**: 新控件只需注册配置，无需修改主组件
3. **类型安全**: 完整的 TypeScript 类型系统
4. **事件统一**: 统一的事件处理和通知机制
5. **条件渲染**: 基于上下文的智能显示逻辑
6. **顺序管理**: 通过 order 属性精确控制显示顺序

### 3. 可维护性提升

- **配置集中**: 所有控件配置集中管理
- **逻辑分离**: 控件逻辑与主组件解耦
- **测试友好**: 每个控件可独立测试
- **文档清晰**: 标准化的配置格式

## 📚 使用示例

### 1. 基础使用

```vue
<template>
  <InputField
    v-model="value"
    type="password"
    clearable
    show-word-limit
    :max-length="20"
  />
</template>
```

### 2. 自定义控件

```typescript
// 1. 创建控件组件
const CustomControl = defineComponent({
  props: ['iconSize', 'disabled'],
  emits: ['custom-action'],
  template: `<button @click="$emit('custom-action')">Custom</button>`,
})

// 2. 创建控件配置
const customConfig: ControlConfig = {
  type: 'custom',
  component: CustomControl,
  position: 'append',
  order: 50,
  condition: context => context.props.showCustom,
  propsFactory: context => ({
    iconSize: context.slotProps.iconSize,
    disabled: context.computed.computedDisabled,
  }),
  eventsFactory: context => ({
    'custom-action': () => context.emit('custom-action'),
  }),
}

// 3. 注册控件
registerControl(customConfig)
```

### 3. 控件事件处理

```vue
<script setup>
  const handleControlEvent = (eventData: ControlEventData) => {
    console.log(`控件 ${eventData.type} 触发了 ${eventData.event} 事件`)

    switch (eventData.type) {
      case 'clear':
        console.log('清除按钮被点击')
        break
      case 'password':
        console.log('密码可见性切换')
        break
      case 'custom':
        console.log('自定义控件事件', eventData.data)
        break
    }
  }
</script>
```

## 🔍 与 VSelectionControl 的对比

### 相似之处

| 特性             | VSelectionControl             | InputControl              |
| ---------------- | ----------------------------- | ------------------------- |
| **基础组件模式** | ✅ 为 Radio/Checkbox 提供基础 | ✅ 为各种输入控件提供基础 |
| **组合模式**     | ✅ 通过插槽系统扩展           | ✅ 通过配置系统扩展       |
| **统一事件处理** | ✅ 统一的事件机制             | ✅ 统一的控件事件系统     |
| **类型安全**     | ✅ 完整的 TS 支持             | ✅ 完整的 TS 支持         |
| **可扩展性**     | ✅ 易于创建新的选择控件       | ✅ 易于注册新的输入控件   |

### 架构差异

| 方面         | VSelectionControl | InputControl        |
| ------------ | ----------------- | ------------------- |
| **扩展方式** | 继承 + 插槽覆盖   | 配置注册 + 动态渲染 |
| **组件关系** | 父子组件关系      | 平级组件关系        |
| **状态管理** | 内置状态计算      | 外部状态注入        |
| **渲染方式** | 模板固定结构      | 动态组件渲染        |
| **配置方式** | Props + 插槽      | 配置对象 + 工厂函数 |

### 适用场景

**VSelectionControl 适合**:

- 选择类控件（Radio、Checkbox、Switch）
- 状态相对固定的场景
- 需要深度自定义渲染的场景

**InputControl 适合**:

- 输入框辅助控件（清除、密码、搜索等）
- 需要动态添加/移除控件的场景
- 控件类型和数量不确定的场景

## 🚀 最佳实践

### 1. 控件设计原则

- **单一职责**: 每个控件只负责一个特定功能
- **无状态**: 控件本身不维护状态，通过 props 接收
- **事件驱动**: 通过事件与外部通信，不直接操作外部状态
- **可配置**: 通过 props 控制控件的行为和外观

### 2. 性能优化

- **条件渲染**: 使用 condition 函数避免不必要的渲染
- **计算缓存**: 利用 computed 缓存控件列表
- **事件优化**: 避免在事件处理器中进行重计算

### 3. 扩展建议

- **控件分组**: 相关控件可以组合成控件组
- **主题支持**: 控件应支持主题系统
- **国际化**: 控件文本应支持多语言
- **无障碍**: 控件应遵循无障碍设计规范

## 📈 未来发展方向

### 1. 增强功能

- **控件组合**: 支持多个控件组合成复合控件
- **动画支持**: 控件显示/隐藏的过渡动画
- **拖拽排序**: 支持用户自定义控件顺序
- **条件联动**: 控件之间的条件依赖关系

### 2. 开发体验

- **可视化配置**: 提供可视化的控件配置工具
- **热重载**: 开发时控件配置的热重载
- **调试工具**: 控件状态和事件的调试面板
- **文档生成**: 自动生成控件文档

### 3. 生态建设

- **控件市场**: 社区贡献的控件库
- **模板系统**: 常用控件组合的模板
- **最佳实践**: 控件开发的最佳实践指南

这种架构设计使得 Speed UI 能够提供功能强大、高度可扩展且易于维护的输入框控件系统！
