/**
 * SpButton - 基于 SpControl 的按钮组件示例
 * 
 * 展示如何使用 SpControl 创建具体的控件实现
 */

import { defineComponent, computed, h } from 'vue'
import type { PropType } from 'vue'
import { SpControl, makeControlProps, useControl } from '../control'
import type { ControlContext } from '../types'

// 按钮特有的属性
export interface SpButtonProps {
  /** 按钮类型 */
  type?: 'button' | 'submit' | 'reset'
  /** 图标名称 */
  icon?: string
  /** 图标位置 */
  iconPosition?: 'left' | 'right'
  /** 是否为块级按钮 */
  block?: boolean
  /** 是否为圆形按钮 */
  circle?: boolean
  /** 加载图标 */
  loadingIcon?: string
}

// 创建按钮 props
export const makeSpButtonProps = () => ({
  ...makeControlProps(),
  type: {
    type: String as PropType<'button' | 'submit' | 'reset'>,
    default: 'button'
  },
  icon: String,
  iconPosition: {
    type: String as PropType<'left' | 'right'>,
    default: 'left'
  },
  block: Boolean,
  circle: Boolean,
  loadingIcon: String,
})

// SpButton 组件
export const SpButton = defineComponent({
  name: 'SpButton',
  
  props: makeSpButtonProps(),
  
  emits: {
    'update:modelValue': (value: any) => true,
    'change': (value: any) => true,
    'focus': (e: FocusEvent) => true,
    'blur': (e: FocusEvent) => true,
    'click': (e: MouseEvent) => true,
    'mouseenter': () => true,
    'mouseleave': () => true,
    'mousedown': () => true,
    'mouseup': () => true,
    'keydown': (e: KeyboardEvent) => true,
  },

  setup(props, { emit, slots, attrs }) {
    const { state, behavior, styles } = useControl(props, emit)

    // 按钮特有的样式计算
    const buttonClasses = computed(() => [
      ...styles.classes.value,
      'sp-button',
      {
        'sp-button--block': props.block,
        'sp-button--circle': props.circle,
        'sp-button--icon-only': props.circle && props.icon && !slots.default,
      }
    ])

    const buttonStyles = computed(() => ({
      ...styles.styles.value,
      ...(props.circle && {
        '--control-border-radius': '50%',
        '--control-aspect-ratio': '1',
      }),
    }))

    // 按钮点击处理
    const handleClick = (e: MouseEvent) => {
      if (state.disabled || state.loading) return
      
      // 按钮特有的点击反馈
      behavior.handleMousedown()
      setTimeout(() => behavior.handleMouseup(), 150)
      
      emit('click', e)
    }

    // 键盘事件处理
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleClick(e as any)
      }
      behavior.handleKeydown(e)
    }

    // 渲染图标
    const renderIcon = (iconName?: string) => {
      if (!iconName) return null
      
      // 这里应该使用实际的图标组件
      return h('span', {
        class: 'sp-button__icon',
        innerHTML: `<i class="icon-${iconName}"></i>` // 简化的图标渲染
      })
    }

    // 渲染加载状态
    const renderLoading = () => {
      if (!state.loading) return null
      
      return h('span', {
        class: 'sp-button__loading'
      }, [
        renderIcon(props.loadingIcon || 'loading')
      ])
    }

    // 渲染按钮内容
    const renderContent = () => {
      if (props.circle) {
        // 圆形按钮：只显示图标或默认插槽
        if (slots.default) {
          return slots.default()
        }
        return renderIcon(props.icon)
      }

      // 普通按钮：显示图标 + 文本
      const content = []
      
      // 左侧图标
      if (props.icon && props.iconPosition === 'left' && !state.loading) {
        content.push(renderIcon(props.icon))
      }
      
      // 主要内容
      if (slots.default) {
        content.push(h('span', { class: 'sp-button__text' }, slots.default()))
      }
      
      // 右侧图标
      if (props.icon && props.iconPosition === 'right' && !state.loading) {
        content.push(renderIcon(props.icon))
      }
      
      return content
    }

    return () => h('button', {
      class: buttonClasses.value,
      style: buttonStyles.value,
      type: props.type,
      disabled: state.disabled,
      tabindex: state.disabled ? -1 : props.tabindex,
      role: props.role || 'button',
      'aria-label': props.ariaLabel,
      'aria-describedby': props.ariaDescribedby,
      'aria-disabled': state.disabled,
      'aria-pressed': state.pressed,
      onClick: handleClick,
      onFocus: behavior.handleFocus,
      onBlur: behavior.handleBlur,
      onMouseenter: behavior.handleMouseenter,
      onMouseleave: behavior.handleMouseleave,
      onKeydown: handleKeydown,
      ...attrs,
    }, [
      // 加载状态
      renderLoading(),
      
      // 按钮内容
      !state.loading && renderContent(),
    ])
  },
})

export default SpButton
