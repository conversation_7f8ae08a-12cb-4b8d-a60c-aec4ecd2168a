/**
 * 按钮上下文管理 composable
 * 基于通用上下文管理器的按钮专用实现
 */

import { type Ref } from 'vue'
import { btnContext } from './useContext'

// 按钮上下文类型（继承自通用组件状态上下文）
export interface BtnContext {
  isHovered: Ref<boolean>
  isPressed: Ref<boolean>
  isFocused: Ref<boolean>
  isActive: Ref<boolean>
  disabled: Ref<boolean>
  loading: Ref<boolean>
  variant?: string
  size?: string
  updateState?: (
    state: Partial<{
      isHovered: boolean
      isPressed: boolean
      isFocused: boolean
      isActive: boolean
      disabled: boolean
      loading: boolean
    }>
  ) => void
}

/**
 * 提供按钮上下文
 */
export const provideBtnContext = btnContext.provideContext

/**
 * 使用按钮上下文
 */
export const useBtnContext = btnContext.useContext

/**
 * 创建按钮上下文
 */
export function createBtnContext(options: {
  isHovered: Ref<boolean>
  isPressed: Ref<boolean>
  disabled: Ref<boolean>
  variant?: string
  size?: string
}): BtnContext {
  return btnContext.createStateContext(options)
}
