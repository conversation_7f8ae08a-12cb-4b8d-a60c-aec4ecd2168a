import { defineComponent, computed, ref } from 'vue'
import { bem<PERSON><PERSON>per } from '@speed-ui/config'
import { btnContext } from '../../composables/useContext'

interface Props {
  direction?: 'horizontal' | 'vertical'
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'outlined' | 'text' | 'underline'
  disabled?: boolean
  gap?: number
}

export default defineComponent({
  name: 'BtnGroup',
  props: {
    direction: {
      type: String as () => Props['direction'],
      default: 'horizontal',
    },
    size: {
      type: String as () => Props['size'],
      default: 'medium',
    },
    variant: {
      type: String as () => Props['variant'],
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    gap: {
      type: Number,
      default: 0,
    },
  },
  setup(props, { slots, attrs }) {
    // 创建 BEM 类名生成器
    const bem = bemHelper('btn-group')

    // 计算类名
    const groupClasses = computed(() => [
      bem.b(),
      bem.m(props.direction || 'horizontal'),
      bem.m(props.size || 'medium'),
      {
        [bem.m('disabled')]: props.disabled,
      },
    ])

    // 计算样式
    const groupStyles = computed(() => {
      const styles: Record<string, string> = {}

      if (props.gap && props.gap > 0) {
        styles['gap'] = `${props.gap}px`
      }

      return styles
    })

    // 为子按钮提供组上下文（使用通用上下文）
    const groupContext = btnContext.createStateContext({
      isHovered: ref(false),
      isPressed: ref(false),
      disabled: computed(() => props.disabled || false),
      variant: props.variant || 'default',
      size: props.size || 'medium',
    })

    // 扩展上下文，添加组特有的属性
    const extendedContext = {
      ...groupContext,
      // 组特有的属性
      isInGroup: true,
      groupDirection: props.direction || 'horizontal',
      groupVariant: props.variant || 'default',
      groupSize: props.size || 'medium',
      groupDisabled: props.disabled || false,
    }

    btnContext.provideContext(extendedContext)

    return () => (
      <div
        class={groupClasses.value}
        style={groupStyles.value}
        {...attrs}
      >
        {slots.default?.()}
      </div>
    )
  },
})
