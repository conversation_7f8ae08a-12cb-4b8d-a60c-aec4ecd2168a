/**
 * SpSwitch 开关组件
 *
 * 基于 SpControl 的 checkbox 类型输入控件，提供开关样式
 */

import { defineComponent, computed } from 'vue'
import type { PropType } from 'vue'
import { SpControl, makeControlProps } from '../control/control'
import { propsFactory } from '../../utils/propsFactory'

// 类型断言，解决 TSX 中的组件类型问题
const Control = SpControl as any

// 开关特有的属性
export interface SpSwitchProps {
  /** 开关的值（选中时的值） */
  value?: any
  /** 标签文本 */
  label?: string
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'left' | 'right'
  /** 自定义颜色 */
  color?: string
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 选中时的文本 */
  checkedText?: string
  /** 未选中时的文本 */
  uncheckedText?: string
  /** 是否显示内部文本 */
  showText?: boolean
  /** 开关变体 */
  variant?: 'default' | 'oversized'
}

// 创建开关 props
export const makeSpSwitchProps = propsFactory(
  {
    ...makeControlProps(),
    type: {
      type: String,
      default: 'checkbox',
    },
    value: {
      default: true, // 开关默认选中值为 true
    },
    label: String,
    showLabel: {
      type: Boolean,
      default: true,
    },
    labelPosition: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
    color: String,
    checkedText: String,
    uncheckedText: String,
    showText: {
      type: Boolean,
      default: false,
    },
    variant: {
      type: String as PropType<'default' | 'oversized'>,
      default: 'default',
    },
  },
  'SpSwitch'
)

// SpSwitch 组件
export const SpSwitch = defineComponent({
  name: 'SpSwitch',

  props: makeSpSwitchProps(),

  emits: {
    'update:modelValue': (value: any) => true,
    change: (e: Event) => true,
    input: (e: Event) => true,
    focus: (e: FocusEvent) => true,
    blur: (e: FocusEvent) => true,
    click: (e: MouseEvent) => true,
  },

  setup(props, { emit, slots, attrs }) {
    // 计算是否选中
    const isChecked = computed(() => {
      if (Array.isArray(props.modelValue)) {
        // 如果 modelValue 是数组，检查是否包含当前值
        return props.modelValue.includes(props.value)
      }
      // 如果 modelValue 不是数组，直接比较
      return !!props.modelValue
    })

    // 开关样式类
    const switchClasses = computed(() => [
      'sp-switch',
      `sp-switch--${props.size}`,
      `sp-switch--${props.variant}`,
      {
        'sp-switch--checked': isChecked.value,
        'sp-switch--disabled': props.disabled,
        'sp-switch--label-left': props.labelPosition === 'left',
        'sp-switch--with-text': props.showText,
      },
    ])

    // 开关样式
    const switchStyles = computed(() => {
      const styles: Record<string, string> = {}

      if (props.color) {
        styles['--sp-color-primary'] = props.color
      }

      return styles
    })

    // 内部文本
    const innerText = computed(() => {
      if (!props.showText) return null
      return isChecked.value ? props.checkedText : props.uncheckedText
    })

    // 处理点击事件
    const handleClick = (e: MouseEvent) => {
      if (props.disabled) return

      // 如果点击的是 input 元素，让它自然处理
      if ((e.target as HTMLElement)?.tagName === 'INPUT') {
        return
      }

      // 否则手动触发 input 的点击
      const inputElement = (e.currentTarget as HTMLElement)?.querySelector(
        'input'
      )
      if (inputElement) {
        inputElement.click()
      }

      emit('click', e)
    }

    return () => {
      // 渲染标签内容
      const labelContent = props.showLabel
        ? props.label || slots.default?.()
        : null

      // 如果没有标签，只返回开关
      if (!labelContent) {
        return (
          <div
            class={switchClasses.value}
            style={switchStyles.value}
            onClick={handleClick}
          >
            <div class="sp-switch__wrapper">
              <Control
                {...props}
                type="checkbox"
                modelValue={props.modelValue}
                trueValue={props.value}
                falseValue={Array.isArray(props.modelValue) ? [] : false}
                class="sp-switch__input"
                onUpdate:modelValue={(value: any) =>
                  emit('update:modelValue', value)
                }
                onChange={(e: Event) => emit('change', e)}
                onInput={(e: Event) => emit('input', e)}
                onFocus={(e: FocusEvent) => emit('focus', e)}
                onBlur={(e: FocusEvent) => emit('blur', e)}
                {...attrs}
              />
              <div class="sp-switch__track">
                <div class="sp-switch__thumb">
                  {innerText.value && (
                    <span class="sp-switch__text">{innerText.value}</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )
      }

      // 有标签的情况
      return (
        <label
          class={switchClasses.value}
          style={switchStyles.value}
        >
          {props.labelPosition === 'left' && (
            <span class="sp-switch__label">{labelContent}</span>
          )}

          <div class="sp-switch__wrapper">
            <Control
              {...props}
              type="checkbox"
              modelValue={props.modelValue}
              trueValue={props.value}
              falseValue={Array.isArray(props.modelValue) ? [] : false}
              class="sp-switch__input"
              onUpdate:modelValue={(value: any) =>
                emit('update:modelValue', value)
              }
              onChange={(e: Event) => emit('change', e)}
              onInput={(e: Event) => emit('input', e)}
              onFocus={(e: FocusEvent) => emit('focus', e)}
              onBlur={(e: FocusEvent) => emit('blur', e)}
              onClick={(e: MouseEvent) => emit('click', e)}
              {...attrs}
            />
            <div class="sp-switch__track">
              <div class="sp-switch__thumb">
                {innerText.value && (
                  <span class="sp-switch__text">{innerText.value}</span>
                )}
              </div>
            </div>
          </div>

          {props.labelPosition === 'right' && (
            <span class="sp-switch__label">{labelContent}</span>
          )}
        </label>
      )
    }
  },
})

export default SpSwitch
