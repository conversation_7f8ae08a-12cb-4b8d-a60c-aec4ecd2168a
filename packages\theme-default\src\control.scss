/**
 * SpControl 通用控件样式
 */

// CSS 变量定义
:root {
  // 控件颜色
  --sp-control-color-primary: #1890ff;
  --sp-control-color-secondary: #6c757d;
  --sp-control-color-success: #52c41a;
  --sp-control-color-warning: #faad14;
  --sp-control-color-error: #ff4d4f;
  --sp-control-color-gray: #d9d9d9;

  // 控件尺寸
  --sp-control-size-small: 24px;
  --sp-control-size-medium: 32px;
  --sp-control-size-large: 40px;

  // 控件间距
  --sp-control-padding-small: 4px 8px;
  --sp-control-padding-medium: 8px 12px;
  --sp-control-padding-large: 12px 16px;

  // 控件字体
  --sp-control-font-size-small: 12px;
  --sp-control-font-size-medium: 14px;
  --sp-control-font-size-large: 16px;

  // 控件圆角
  --sp-control-border-radius: 6px;

  // 控件阴影
  --sp-control-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --sp-control-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
  --sp-control-shadow-focus: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

// 基础控件样式
.sp-control {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--control-size, var(--sp-control-size-medium));
  padding: var(--control-padding, var(--sp-control-padding-medium));
  font-size: var(--control-font-size, var(--sp-control-font-size-medium));
  font-weight: 500;
  line-height: 1.4;
  border: 1px solid transparent;
  border-radius: var(--control-border-radius, var(--sp-control-border-radius));
  background-color: transparent;
  color: inherit;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;

  // 聚焦状态
  &:focus-visible {
    box-shadow: var(--sp-control-shadow-focus);
  }

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // 只读状态
  &--readonly {
    cursor: default;
  }

  // 加载状态
  &--loading {
    cursor: wait;
  }

  // 错误状态
  &--error {
    border-color: var(--sp-control-color-error);
    color: var(--sp-control-color-error);
  }
}

// 控件变体样式
.sp-control--default {
  background-color: #fff;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.85);

  &:hover:not(.sp-control--disabled) {
    border-color: var(--sp-control-color-primary);
    color: var(--sp-control-color-primary);
  }

  &.sp-control--pressed {
    background-color: #f5f5f5;
  }
}

.sp-control--primary {
  background-color: var(--sp-control-color-primary);
  border-color: var(--sp-control-color-primary);
  color: #fff;

  &:hover:not(.sp-control--disabled) {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  &.sp-control--pressed {
    background-color: #096dd9;
    border-color: #096dd9;
  }
}

.sp-control--secondary {
  background-color: var(--sp-control-color-secondary);
  border-color: var(--sp-control-color-secondary);
  color: #fff;

  &:hover:not(.sp-control--disabled) {
    opacity: 0.8;
  }
}

.sp-control--success {
  background-color: var(--sp-control-color-success);
  border-color: var(--sp-control-color-success);
  color: #fff;

  &:hover:not(.sp-control--disabled) {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.sp-control--warning {
  background-color: var(--sp-control-color-warning);
  border-color: var(--sp-control-color-warning);
  color: #fff;

  &:hover:not(.sp-control--disabled) {
    background-color: #ffc53d;
    border-color: #ffc53d;
  }
}

.sp-control--error {
  background-color: var(--sp-control-color-error);
  border-color: var(--sp-control-color-error);
  color: #fff;

  &:hover:not(.sp-control--disabled) {
    background-color: #ff7875;
    border-color: #ff7875;
  }
}

// 按钮特有样式
.sp-button {
  gap: 8px;

  &--block {
    width: 100%;
  }

  &--circle {
    width: var(--control-size, var(--sp-control-size-medium));
    height: var(--control-size, var(--sp-control-size-medium));
    padding: 0;
    border-radius: 50%;
    aspect-ratio: 1;
  }

  &--icon-only {
    min-width: var(--control-size, var(--sp-control-size-medium));
  }

  &__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }

  &__text {
    white-space: nowrap;
  }

  &__loading {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    .sp-button__icon {
      animation: sp-control-spin 1s linear infinite;
    }
  }
}

// 开关样式在 switch.scss 中定义

// 动画
@keyframes sp-control-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sp-control {
    min-height: 44px; // 增加触摸目标大小
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .sp-control {
    border-width: 2px;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .sp-control {
    transition: none;
  }

  .sp-button__loading .sp-button__icon {
    animation: none;
  }
}
