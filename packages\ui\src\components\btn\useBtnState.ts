/**
 * 按钮状态管理 composable
 * 统一处理按钮的状态计算和样式生成
 */

import { computed, inject, type Ref } from 'vue'
import { bemHelper } from '@speed-ui/config'
import type { BtnStyleProps, BtnStateInject } from './types'

export interface UseBtnStateOptions {
  props: BtnStyleProps | Ref<BtnStyleProps>
  btnBaseRef?: Ref<any>
}

/**
 * 按钮状态管理 hook
 * 处理状态计算、类名生成和样式生成
 */
export function useBtnState(options: UseBtnStateOptions) {
  const { props, btnBaseRef } = options

  // 从子组件注入状态
  const btnState = inject<BtnStateInject | null>('btnState', null)

  // 创建 BEM 类名生成器
  const bem = bemHelper('btn')

  // 获取响应式 props
  const reactiveProps = computed(() => {
    return typeof props === 'object' && 'value' in props ? props.value : props
  })

  // 计算当前状态（优先级：props > inject > ref）
  const currentIsHovered = computed(
    () =>
      reactiveProps.value.isHovered ||
      btnState?.isHovered?.value ||
      btnBaseRef?.value?.isHovered ||
      false
  )

  const currentIsPressed = computed(
    () =>
      reactiveProps.value.isPressed ||
      btnState?.isPressed?.value ||
      btnBaseRef?.value?.isPressed ||
      false
  )

  // 计算类名
  const buttonClasses = computed(() => [
    bem.b(),
    bem.m(reactiveProps.value.variant || 'default'),
    reactiveProps.value.size === 'medium'
      ? ''
      : bem.m(reactiveProps.value.size || 'medium'),
    {
      [bem.m('disabled')]: reactiveProps.value.disabled,
      [bem.m('hovered')]: currentIsHovered.value,
      [bem.m('pressed')]: currentIsPressed.value,
      [bem.m('circle')]: reactiveProps.value.circle,
    },
  ])

  // 计算样式
  const buttonStyles = computed(() => {
    const styles: Record<string, string> = {}

    // 根据状态添加样式变量
    if (currentIsHovered.value && !reactiveProps.value.disabled) {
      styles['--btn-state'] = 'hovered'
    }
    if (currentIsPressed.value && !reactiveProps.value.disabled) {
      styles['--btn-state'] = 'pressed'
    }

    // 添加圆角样式
    if (reactiveProps.value.circle) {
      // 圆形按钮：使用 50% 圆角
      styles['--btn-border-radius'] = '50%'
    } else if (reactiveProps.value.rounded !== undefined) {
      // 普通按钮：使用指定的圆角值
      styles['--btn-border-radius'] = `${reactiveProps.value.rounded * 4}px`
    }

    return styles
  })

  return {
    // 状态
    currentIsHovered,
    currentIsPressed,
    // 样式
    buttonClasses,
    buttonStyles,
    // 工具
    bem,
  }
}
