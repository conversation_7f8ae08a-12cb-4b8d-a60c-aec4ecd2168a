# 通用上下文管理器使用指南

## 🎯 概述

通用上下文管理器提供了一套标准化的组件状态共享方案，避免为每个组件重复创建上下文管理代码。

## 🚀 快速开始

### 1. 使用预定义的组件上下文

```typescript
// 按钮组件
import { btnContext } from '@/composables/useContext'

// 在父组件中提供上下文
const context = btnContext.createStateContext({
  isHovered: ref(false),
  isPressed: ref(false),
  disabled: ref(false),
  variant: 'primary',
  size: 'medium'
})

btnContext.provideContext(context)

// 在子组件中使用上下文
const { context, hasContext } = btnContext.useContext()
```

### 2. 创建自定义组件上下文

```typescript
// 创建卡片组件上下文
export const cardContext = createComponentStateContext('card')

// 使用方式与预定义上下文相同
const context = cardContext.createStateContext({
  isHovered: ref(false),
  isPressed: ref(false),
  disabled: ref(false),
  variant: 'outlined',
  size: 'medium'
})
```

### 3. 创建简单上下文

```typescript
// 创建主题上下文
const themeContext = createSimpleContext<{
  theme: 'light' | 'dark'
  primaryColor: string
}>('theme', {
  theme: 'light',
  primaryColor: '#1890ff'
})

// 提供上下文
themeContext.provideContext({
  theme: 'dark',
  primaryColor: '#722ed1'
})

// 使用上下文
const { context } = themeContext.useContext()
```

## 📋 预定义上下文

### 可用的组件上下文

- `btnContext` - 按钮组件
- `inputContext` - 输入框组件
- `formItemContext` - 表单项组件
- `menuContext` - 菜单组件
- `tableContext` - 表格组件

### 组件状态上下文包含的状态

```typescript
interface ComponentStateContext {
  isHovered: Ref<boolean>     // 悬停状态
  isPressed: Ref<boolean>     // 按下状态
  isFocused: Ref<boolean>     // 焦点状态
  isActive: Ref<boolean>      // 激活状态
  disabled: Ref<boolean>      // 禁用状态
  loading: Ref<boolean>       // 加载状态
  variant?: string            // 变体
  size?: string              // 尺寸
  updateState?: Function      // 状态更新方法
}
```

## 🔧 实际应用示例

### 按钮组件

```typescript
// btn.tsx
import { btnContext } from '@/composables/useContext'

export default defineComponent({
  setup(props) {
    const isHovered = ref(false)
    const isPressed = ref(false)
    const disabled = computed(() => props.disabled)
    
    // 创建并提供上下文
    const context = btnContext.createStateContext({
      isHovered,
      isPressed,
      disabled,
      variant: props.variant,
      size: props.size
    })
    
    btnContext.provideContext(context)
    
    return () => (
      <button>
        <ButtonIcon />
        <ButtonText />
      </button>
    )
  }
})
```

### 按钮图标子组件

```typescript
// button-icon.tsx
import { btnContext } from '@/composables/useContext'

export default defineComponent({
  setup() {
    const { context } = btnContext.useContext()
    
    const iconClass = computed(() => [
      'btn-icon',
      {
        'btn-icon--hovered': context?.isHovered.value,
        'btn-icon--disabled': context?.disabled.value,
      }
    ])
    
    return () => (
      <i class={iconClass.value}>
        <slot />
      </i>
    )
  }
})
```

### 输入框组件

```typescript
// input.tsx
import { inputContext } from '@/composables/useContext'

export default defineComponent({
  setup(props) {
    const isFocused = ref(false)
    const disabled = computed(() => props.disabled)
    
    // 创建输入框上下文
    const context = inputContext.createStateContext({
      isHovered: ref(false),
      isPressed: ref(false),
      isFocused,
      disabled,
      variant: props.variant,
      size: props.size
    })
    
    inputContext.provideContext(context)
    
    return () => (
      <div class="input-wrapper">
        <InputField />
        <InputLabel />
        <InputHelperText />
      </div>
    )
  }
})
```

## 🌟 优势

1. **标准化** - 所有组件使用相同的上下文模式
2. **类型安全** - 完整的 TypeScript 支持
3. **可扩展** - 轻松添加新的组件上下文
4. **性能优化** - 基于 Vue 的依赖注入系统
5. **开发效率** - 减少重复代码，提高开发速度

## 🔄 迁移指南

### 从旧的上下文迁移

```typescript
// 旧方式
const BTN_CONTEXT_KEY = Symbol('btn-context')
provide(BTN_CONTEXT_KEY, context)
const context = inject(BTN_CONTEXT_KEY)

// 新方式
import { btnContext } from '@/composables/useContext'
btnContext.provideContext(context)
const { context } = btnContext.useContext()
```

这样你就有了一个通用的上下文管理系统，不需要为每个组件重复创建上下文代码了！
