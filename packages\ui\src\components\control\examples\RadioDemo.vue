<template>
  <div class="radio-demo">
    <h1>SpRadio 单选按钮演示</h1>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>🎯 基础用法</h2>
      <div class="demo-row">
        <SpRadio
          v-model="basicValue"
          value="option1"
          label="选项 1"
        />
        <SpRadio
          v-model="basicValue"
          value="option2"
          label="选项 2"
        />
        <SpRadio
          v-model="basicValue"
          value="option3"
          label="选项 3"
        />
      </div>
      <p class="demo-info">当前选择: {{ basicValue }}</p>
    </section>

    <!-- 不同尺寸 -->
    <section class="demo-section">
      <h2>📏 不同尺寸</h2>
      <div class="demo-row">
        <div class="size-group">
          <h3>Small</h3>
          <SpRadio
            v-model="sizeValue"
            value="small1"
            label="小尺寸 1"
            size="small"
          />
          <SpRadio
            v-model="sizeValue"
            value="small2"
            label="小尺寸 2"
            size="small"
          />
        </div>

        <div class="size-group">
          <h3>Medium</h3>
          <SpRadio
            v-model="sizeValue"
            value="medium1"
            label="中等尺寸 1"
            size="medium"
          />
          <SpRadio
            v-model="sizeValue"
            value="medium2"
            label="中等尺寸 2"
            size="medium"
          />
        </div>

        <div class="size-group">
          <h3>Large</h3>
          <SpRadio
            v-model="sizeValue"
            value="large1"
            label="大尺寸 1"
            size="large"
          />
          <SpRadio
            v-model="sizeValue"
            value="large2"
            label="大尺寸 2"
            size="large"
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ sizeValue }}</p>
    </section>

    <!-- 标签位置 -->
    <section class="demo-section">
      <h2>🏷️ 标签位置</h2>
      <div class="demo-row">
        <div class="label-group">
          <h3>标签在右侧（默认）</h3>
          <SpRadio
            v-model="labelValue"
            value="right1"
            label="右侧标签 1"
            label-position="right"
          />
          <SpRadio
            v-model="labelValue"
            value="right2"
            label="右侧标签 2"
            label-position="right"
          />
        </div>

        <div class="label-group">
          <h3>标签在左侧</h3>
          <SpRadio
            v-model="labelValue"
            value="left1"
            label="左侧标签 1"
            label-position="left"
          />
          <SpRadio
            v-model="labelValue"
            value="left2"
            label="左侧标签 2"
            label-position="left"
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ labelValue }}</p>
    </section>

    <!-- 自定义颜色 -->
    <section class="demo-section">
      <h2>🎨 自定义颜色</h2>
      <div class="demo-row">
        <SpRadio
          v-model="colorValue"
          value="red"
          label="红色"
          color="#ff4d4f"
        />
        <SpRadio
          v-model="colorValue"
          value="green"
          label="绿色"
          color="#52c41a"
        />
        <SpRadio
          v-model="colorValue"
          value="blue"
          label="蓝色"
          color="#1890ff"
        />
        <SpRadio
          v-model="colorValue"
          value="purple"
          label="紫色"
          color="#722ed1"
        />
      </div>
      <p class="demo-info">当前选择: {{ colorValue }}</p>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🔒 状态演示</h2>
      <div class="demo-row">
        <div class="state-group">
          <h3>正常状态</h3>
          <SpRadio
            v-model="stateValue"
            value="normal1"
            label="正常选项 1"
          />
          <SpRadio
            v-model="stateValue"
            value="normal2"
            label="正常选项 2"
          />
        </div>

        <div class="state-group">
          <h3>禁用状态</h3>
          <SpRadio
            v-model="stateValue"
            value="disabled1"
            label="禁用选项 1"
            disabled
          />
          <SpRadio
            v-model="stateValue"
            value="disabled2"
            label="禁用选项 2（已选中）"
            disabled
          />
        </div>

        <div class="state-group">
          <h3>只读状态</h3>
          <SpRadio
            v-model="stateValue"
            value="readonly1"
            label="只读选项 1"
            readonly
          />
          <SpRadio
            v-model="stateValue"
            value="readonly2"
            label="只读选项 2"
            readonly
          />
        </div>
      </div>
      <p class="demo-info">当前选择: {{ stateValue }}</p>
    </section>

    <!-- 使用插槽 -->
    <section class="demo-section">
      <h2>🎪 使用插槽</h2>
      <div class="demo-row">
        <SpRadio
          v-model="slotValue"
          value="slot1"
          :show-label="false"
        >
          <span style="color: #1890ff; font-weight: bold">🚀 自定义内容 1</span>
        </SpRadio>

        <SpRadio
          v-model="slotValue"
          value="slot2"
          :show-label="false"
        >
          <div style="display: flex; align-items: center; gap: 8px">
            <span
              style="
                background: #f0f0f0;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
              "
            >
              NEW
            </span>
            <span>带标签的选项</span>
          </div>
        </SpRadio>

        <SpRadio
          v-model="slotValue"
          value="slot3"
          :show-label="false"
        >
          <div
            style="
              border: 1px solid #d9d9d9;
              padding: 8px;
              border-radius: 4px;
              background: #fafafa;
            "
          >
            <div style="font-weight: bold">复杂选项</div>
            <div style="font-size: 12px; color: #666">包含描述信息的选项</div>
          </div>
        </SpRadio>
      </div>
      <p class="demo-info">当前选择: {{ slotValue }}</p>
    </section>

    <!-- 基础 SpControl 演示 -->
    <section class="demo-section">
      <h2>⚙️ 基础 SpControl</h2>
      <div class="demo-row">
        <div class="control-group">
          <h3>文本输入</h3>
          <SpControl
            v-model="textValue"
            type="text"
            placeholder="请输入文本"
          />
        </div>

        <div class="control-group">
          <h3>密码输入</h3>
          <SpControl
            v-model="passwordValue"
            type="password"
            placeholder="请输入密码"
          />
        </div>

        <div class="control-group">
          <h3>数字输入</h3>
          <SpControl
            v-model="numberValue"
            type="number"
            placeholder="请输入数字"
            min="0"
            max="100"
          />
        </div>

        <div class="control-group">
          <h3>复选框</h3>
          <SpControl
            v-model="checkboxValue"
            type="checkbox"
          />
        </div>
      </div>
      <div class="demo-info">
        <p>
          <strong>文本值:</strong>
          {{ textValue }}
        </p>
        <p>
          <strong>密码值:</strong>
          {{ passwordValue }}
        </p>
        <p>
          <strong>数字值:</strong>
          {{ numberValue }}
        </p>
        <p>
          <strong>复选框:</strong>
          {{ checkboxValue }}
        </p>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import SpRadio from '../../radio/radio'
  import SpControl from '../control'

  // 响应式数据
  const basicValue = ref('option1')
  const sizeValue = ref('medium1')
  const labelValue = ref('right1')
  const colorValue = ref('blue')
  const stateValue = ref('disabled2') // 预选中禁用项
  const slotValue = ref('slot1')

  // SpControl 演示数据
  const textValue = ref('')
  const passwordValue = ref('')
  const numberValue = ref(0)
  const checkboxValue = ref(false)
</script>

<style scoped>
  .radio-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fff;
  }

  .demo-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1890ff;
  }

  .demo-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }

  .size-group,
  .label-group,
  .state-group,
  .control-group {
    flex: 1;
    min-width: 200px;
  }

  .size-group h3,
  .label-group h3,
  .state-group h3,
  .control-group h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
  }

  .size-group .sp-radio,
  .label-group .sp-radio,
  .state-group .sp-radio {
    display: block;
    margin-bottom: 8px;
  }

  .demo-info {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 6px;
    font-family: monospace;
    margin-top: 16px;
  }

  .demo-info p {
    margin: 4px 0;
  }

  /* 单选按钮样式 */
  .sp-radio {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-right: 16px;
    margin-bottom: 8px;
    cursor: pointer;
  }

  .sp-radio__input {
    margin: 0;
  }

  .sp-radio__label {
    user-select: none;
  }

  .sp-radio--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .sp-radio--label-left {
    flex-direction: row-reverse;
  }
</style>
