/**
 * SpControl 通用输入控件基础组件
 *
 * 这是一个通用的输入控件基础组件，为所有输入类型控件提供统一的基础功能。
 * 它是 Speed UI 中 SpInput、SpRadio、SpCheckbox、SpSwitch 等组件的底层实现。
 *
 * 主要功能：
 * 1. 提供统一的输入控件基础结构和逻辑
 * 2. 处理各种输入类型的值管理和事件处理
 * 3. 管理焦点状态和交互行为
 * 4. 支持表单集成和验证
 * 5. 提供无障碍功能支持
 * 6. 支持自定义插槽来扩展功能
 *
 * 设计模式：
 * - 采用组合模式，通过插槽允许子组件自定义渲染
 * - 使用 Composable 模式分离逻辑和视图
 * - 参考 Vuetify VSelectionControl 的设计理念
 */

// Styles

// Utilities
import { computed, ref, watch, useId, defineComponent, shallowRef } from 'vue'
import { propsFactory } from '../../utils/propsFactory'
import { bemHelper } from '@speed-ui/config'

// Types
import type { PropType, ExtractPropTypes, Ref, VNode } from 'vue'

// 输入控件类型
export type InputType =
  | 'text'
  | 'password'
  | 'email'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'radio'
  | 'checkbox'
  | 'hidden'
  | 'file'
  | 'range'
  | 'color'
  | 'date'
  | 'datetime-local'
  | 'month'
  | 'time'
  | 'week'

// 控件尺寸类型
export type ControlSize = 'small' | 'medium' | 'large'

/**
 * 控件插槽的参数类型
 * 提供给插槽使用的所有必要数据和方法
 */
export type ControlSlot = {
  inputRef: Ref<HTMLInputElement | undefined> // 输入元素的引用
  inputNode: VNode // 原生 input 元素的 VNode
  inputProps: Record<string, any> // 传递给输入元素的属性
  focus: () => void // 聚焦方法
  blur: () => void // 失焦方法
  select: () => void // 选择文本方法
  id: string // 输入元素的唯一 ID
  isInteractive: Ref<boolean> // 是否可交互
  isFocused: Ref<boolean> // 是否聚焦
}

/**
 * SpControl 组件的插槽类型定义
 */
export type SpControlSlots = {
  /** 默认插槽：用于自定义控件的外观 */
  default: ControlSlot
  /** 输入插槽：用于完全自定义输入控件的渲染 */
  input: ControlSlot
}

/**
 * 创建 SpControl 组件的 props 定义
 *
 * 包含的属性：
 * - modelValue: 双向绑定的值
 * - type: 输入类型
 * - 状态属性: disabled、readonly、loading 等
 * - 验证属性: required、pattern、min/max 等
 * - 外观属性: size、color、variant 等
 * - 选择控件属性: trueValue、falseValue（用于 radio/checkbox）
 * - 表单属性: name、form 等
 * - 无障碍属性: aria-* 等
 */
export const makeSpControlProps = propsFactory(
  {
    // 基础 input 属性
    modelValue: null,
    type: {
      type: String as PropType<InputType>,
      default: 'text',
    },
    name: String,
    id: String,
    value: null,

    // 选择控件属性（参考 VSelectionControl）
    trueValue: null, // 选中时的值（可以是任意类型）
    falseValue: null, // 未选中时的值（可以是任意类型）

    // 状态属性
    disabled: Boolean,
    readonly: Boolean,
    required: Boolean,
    autofocus: Boolean,
    loading: Boolean, // 加载状态
    error: Boolean, // 错误状态

    // 验证属性
    pattern: String,
    min: [String, Number],
    max: [String, Number],
    step: [String, Number],
    minlength: [String, Number],
    maxlength: [String, Number],

    // 外观属性
    size: {
      type: String as PropType<ControlSize>,
      default: 'medium',
    },
    placeholder: String,
    color: String, // 主题颜色
    baseColor: String, // 基础颜色（未激活状态）

    // 表单属性
    form: String,
    formaction: String,
    formenctype: String,
    formmethod: String,
    formnovalidate: Boolean,
    formtarget: String,

    // 文件上传属性
    accept: String,
    multiple: Boolean,

    // 其他属性
    autocomplete: String,
    tabindex: [String, Number],

    // 无障碍属性
    ariaLabel: String,
    ariaDescribedby: String,
    ariaInvalid: Boolean,
    role: String,

    // 值比较函数（用于复杂对象比较）
    valueComparator: {
      type: Function as PropType<(a: any, b: any) => boolean>,
      default: (a: any, b: any) => a === b,
    },
  },
  'SpControl'
)

/**
 * 控件的核心逻辑 Composable
 *
 * 这个函数封装了所有输入控件的通用逻辑：
 * 1. 处理各种输入类型的值管理
 * 2. 计算控件状态（聚焦、交互等）
 * 3. 管理事件处理
 * 4. 处理选择控件的特殊逻辑（radio/checkbox）
 * 5. 提供统一的 API 接口
 *
 * @param props 组件属性
 * @param emit 事件发射器
 * @returns 返回控件需要的所有状态和方法
 */
export function useControl(
  props: ExtractPropTypes<ReturnType<typeof makeSpControlProps>>,
  emit: any
) {
  const inputRef = ref<HTMLInputElement>()
  const uid = useId()
  const id = computed(() => props.id || `input-${uid}`)
  const isFocused = shallowRef(false)
  const isInteractive = computed(() => !props.disabled && !props.readonly)

  // 计算选中时的值：优先级 trueValue > value > true
  const trueValue = computed(() =>
    props.trueValue !== undefined
      ? props.trueValue
      : props.value !== undefined
      ? props.value
      : true
  )

  // 计算未选中时的值：优先级 falseValue > false
  const falseValue = computed(() =>
    props.falseValue !== undefined ? props.falseValue : false
  )

  // 判断是否为多选模式：modelValue 是数组
  const isMultiple = computed(() => Array.isArray(props.modelValue))

  // 判断是否为选择控件类型
  const isSelectionControl = computed(
    () => props.type === 'radio' || props.type === 'checkbox'
  )

  // 计算实际的值
  const computedValue = computed(() => {
    return props.modelValue !== undefined ? props.modelValue : props.value
  })

  // 监听 modelValue 变化，同步到 input
  watch(
    () => props.modelValue,
    newValue => {
      if (inputRef.value && !isSelectionControl.value) {
        const stringValue = String(newValue || '')
        if (inputRef.value.value !== stringValue) {
          inputRef.value.value = stringValue
        }
      }
    }
  )

  /**
   * 核心的选中状态计算属性（用于选择控件）
   *
   * get: 根据当前值判断是否选中
   * - 多选模式：检查数组中是否包含 trueValue
   * - 单选模式：直接比较值是否等于 trueValue
   *
   * set: 更新选中状态
   * - 多选模式：添加或移除数组中的值
   * - 单选模式：直接设置 trueValue 或 falseValue
   */
  const model = computed({
    get() {
      if (!isSelectionControl.value) {
        return computedValue.value
      }

      const val = computedValue.value
      return isMultiple.value
        ? // 多选模式：检查数组中是否包含目标值
          Array.isArray(val) &&
            val.some((v: any) => props.valueComparator(v, trueValue.value))
        : // 单选模式：直接比较值
          props.valueComparator(val, trueValue.value)
    },
    set(val: any) {
      if (props.readonly) return

      let newVal = val

      if (isSelectionControl.value) {
        // 选择控件的值处理
        const currentValue = val ? trueValue.value : falseValue.value

        if (isMultiple.value) {
          // 多选模式：添加或移除数组中的值
          const currentArray = Array.isArray(computedValue.value)
            ? computedValue.value
            : []

          newVal = val
            ? [...currentArray, currentValue] // 添加值
            : currentArray.filter(
                (item: any) => !props.valueComparator(item, trueValue.value)
              ) // 移除值
        } else {
          newVal = currentValue
        }
      }

      emit('update:modelValue', newVal)
    },
  })

  // 输入事件处理
  const handleInput = (e: Event) => {
    if (!isInteractive.value) {
      // 非交互状态下恢复原值
      if (inputRef.value && isSelectionControl.value) {
        inputRef.value.checked = !!model.value
      }
      return
    }

    const target = e.target as HTMLInputElement
    let value: any = target.value

    // 根据类型处理值
    if (props.type === 'number') {
      value = target.valueAsNumber
    } else if (isSelectionControl.value) {
      value = target.checked
      // 对于选择控件，使用 model 的 setter
      model.value = value
      emit('input', e)
      return
    } else if (props.type === 'file') {
      value = target.files
    }

    emit('update:modelValue', value)
    emit('input', e)
  }

  // 变更事件处理
  const handleChange = (e: Event) => {
    if (!isInteractive.value) return

    const target = e.target as HTMLInputElement
    let value: any = target.value

    // 根据类型处理值
    if (props.type === 'number') {
      value = target.valueAsNumber
    } else if (isSelectionControl.value) {
      value = target.checked
      // 对于选择控件，使用 model 的 setter
      model.value = value
      emit('change', e)
      return
    } else if (props.type === 'file') {
      value = target.files
    }

    emit('update:modelValue', value)
    emit('change', e)
  }

  // 焦点事件处理
  const handleFocus = (e: FocusEvent) => {
    if (!isInteractive.value) return
    isFocused.value = true
    emit('focus', e)
  }

  const handleBlur = (e: FocusEvent) => {
    isFocused.value = false
    emit('blur', e)
  }

  // 点击事件处理
  const handleClick = (e: MouseEvent) => {
    if (!isInteractive.value) return
    emit('click', e)
  }

  // 键盘事件处理
  const handleKeydown = (e: KeyboardEvent) => {
    if (!isInteractive.value) return
    emit('keydown', e)
  }

  const handleKeyup = (e: KeyboardEvent) => {
    if (!isInteractive.value) return
    emit('keyup', e)
  }

  const handleKeypress = (e: KeyboardEvent) => {
    if (!isInteractive.value) return
    emit('keypress', e)
  }

  // BEM 类名助手
  const bem = bemHelper('control')

  // 计算组件类名
  const controlClasses = computed(() => [
    bem.b(),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('readonly')]: props.readonly,
      [bem.m('loading')]: props.loading,
      [bem.m('error')]: props.error,
      [bem.m('focused')]: isFocused.value,
      [bem.m('interactive')]: isInteractive.value,
      [bem.m(props.size)]: props.size !== 'medium',
      ...(props.color && { [bem.m(props.color)]: true }),
    },
  ])

  // 计算 input 属性
  const inputProps = computed(() => {
    const props_: Record<string, any> = {
      ref: inputRef,
      type: props.type,
      name: props.name,
      id: id.value,
      disabled: props.disabled,
      readonly: props.readonly,
      required: props.required,
      autofocus: props.autofocus,
      placeholder: props.placeholder,
      pattern: props.pattern,
      min: props.min,
      max: props.max,
      step: props.step,
      minlength: props.minlength,
      maxlength: props.maxlength,
      form: props.form,
      formaction: props.formaction,
      formenctype: props.formenctype,
      formmethod: props.formmethod,
      formnovalidate: props.formnovalidate,
      formtarget: props.formtarget,
      accept: props.accept,
      multiple: props.multiple,
      autocomplete: props.autocomplete,
      tabindex: props.tabindex,
      'aria-label': props.ariaLabel,
      'aria-describedby': props.ariaDescribedby,
      'aria-invalid': props.ariaInvalid || props.error,
      role: props.role,
      class: bem.e('input'),
      onInput: handleInput,
      onChange: handleChange,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onClick: handleClick,
      onKeydown: handleKeydown,
      onKeyup: handleKeyup,
      onKeypress: handleKeypress,
    }

    // 设置值
    if (isSelectionControl.value) {
      props_.checked = !!model.value
      if (props.value !== undefined) {
        props_.value = props.value
      }
    } else if (props.type === 'file') {
      // file 类型不设置 value
    } else {
      props_.value = computedValue.value || ''
    }

    return props_
  })

  // 暴露方法
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  const select = () => {
    inputRef.value?.select()
  }

  return {
    // 引用和状态
    inputRef,
    isFocused,
    isInteractive,

    // 计算属性
    inputProps,
    controlClasses,
    model,
    id,

    // 方法
    focus,
    blur,
    select,

    // BEM 助手
    bem,
  }
}

/**
 * SpControl 组件定义
 *
 * 这是一个基础组件，为所有输入控件提供统一的基础实现。
 */
export const SpControl = defineComponent({
  name: 'SpControl',

  inheritAttrs: false,

  props: makeSpControlProps(),

  emits: {
    'update:modelValue': (value: any) => true,
    change: (e: Event) => true,
    input: (e: Event) => true,
    focus: (e: FocusEvent) => true,
    blur: (e: FocusEvent) => true,
    click: (e: MouseEvent) => true,
    keydown: (e: KeyboardEvent) => true,
    keyup: (e: KeyboardEvent) => true,
    keypress: (e: KeyboardEvent) => true,
  },

  setup(props, { attrs, slots, emit }) {
    const {
      inputRef,
      inputProps,
      controlClasses,
      isFocused,
      isInteractive,
      focus,
      blur,
      select,
      id,
    } = useControl(props, emit)

    return () => {
      // 创建 input 节点
      const inputNode = (
        <input
          {...inputProps.value}
          {...attrs}
        />
      ) as VNode

      // 插槽参数
      const slotProps: ControlSlot = {
        inputRef,
        inputNode,
        inputProps: { ...inputProps.value, ...attrs },
        focus,
        blur,
        select,
        id: id.value,
        isInteractive,
        isFocused,
      }

      // 如果有 input 插槽，使用插槽内容
      if (slots.input) {
        return <div class={controlClasses.value}>{slots.input(slotProps)}</div>
      }

      // 如果有默认插槽，使用插槽内容
      if (slots.default) {
        return slots.default(slotProps)
      }

      // 否则渲染默认的 input 元素
      return <div class={controlClasses.value}>{inputNode}</div>
    }
  },
})

export type SpControlInstance = InstanceType<typeof SpControl>

// 向后兼容的导出
export const makeControlProps = makeSpControlProps
export default SpControl
