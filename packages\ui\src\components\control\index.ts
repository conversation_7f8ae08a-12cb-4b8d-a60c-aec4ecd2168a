/**
 * SpControl 通用控件系统导出
 */

// 核心组件和函数
export { default as SpControl } from './control'
export {
  makeControlProps,
  useControlState,
  useControlBehavior,
  useControlStyles,
  useControl,
} from './control'

// 类型定义
export type {
  ControlSize,
  ControlVariant,
  ControlState,
  ControlStyles,
  ControlBehavior,
  ControlContext,
  ControlEmits,
  ControlProps,
  ControlSlots,
  ControlConfig,
  ControlRegistry,
  ControlInstance,
  UseControlReturn,
  ControlFactory,
  ControlHOC,
} from './types'

// 示例组件
export { default as SpButton } from './examples/SpButton'
export { makeSpButtonProps } from './examples/SpButton'
export type { SpButtonProps } from './examples/SpButton'

export { default as SpSwitch } from './examples/SpSwitch'
export { makeSpSwitchProps } from './examples/SpSwitch'
export type { SpSwitchProps } from './examples/SpSwitch'
