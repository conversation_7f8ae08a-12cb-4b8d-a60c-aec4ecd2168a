/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// JSX 类型声明
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any
      button: any
      span: any
      div: any
    }
    interface ElementAttributesProperty {
      $props: {}
    }
  }
}
