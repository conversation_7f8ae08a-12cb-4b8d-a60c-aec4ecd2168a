/**
 * SpCheckbox 复选框样式
 */

.sp-checkbox {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;

  &__input {
    margin: 0;
    cursor: pointer;

    // 设置复选框的基础样式
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #d9d9d9;
    border-radius: 2px;
    background-color: #ffffff;
    position: relative;
    transition: all 0.2s ease, box-shadow 0.2s ease;

    // 为勾号添加基础样式（未选中时隐藏）
    &::after {
      content: '';
      position: absolute;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    // 悬停效果
    &:hover {
      border-color: var(--sp-color-primary, #1890ff);
      box-shadow: 0 0 0 8px
        var(--sp-color-primary-lightest, rgba(24, 144, 255, 0.1));
    }

    // 选中状态
    &:checked {
      border-color: var(--sp-color-primary, #1890ff);
      background-color: var(--sp-color-primary, #1890ff);

      // 选中状态的勾号 - 完美居中显示
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 4px;
        height: 7px;
        border: 2px solid #ffffff;
        border-top: none;
        border-left: none;
        transform: translate(-50%, -60%) rotate(45deg);
        opacity: 1;
        transition: opacity 0.2s ease;
      }

      // 选中状态的悬停效果
      &:hover {
        background-color: var(--sp-color-primary-hover, #40a9ff);
        border-color: var(--sp-color-primary-hover, #40a9ff);
        box-shadow: 0 0 0 8px
          var(--sp-color-primary-lightest, rgba(24, 144, 255, 0.15));
      }
    }

    // 不确定状态
    &:indeterminate {
      border-color: var(--sp-color-primary, #1890ff);
      background-color: var(--sp-color-primary, #1890ff);

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8px;
        height: 2px;
        background-color: #ffffff;
        border-radius: 1px;
        transform: translate(-50%, -50%);
        opacity: 1;
        transition: opacity 0.2s ease;
      }
    }

    // 禁用状态
    &:disabled {
      border-color: #d9d9d9;
      background-color: #f5f5f5;
      cursor: not-allowed;

      &:checked {
        background-color: #d9d9d9;
        border-color: #d9d9d9;

        &::after {
          border-color: #ffffff;
        }
      }

      &:indeterminate {
        background-color: #d9d9d9;
        border-color: #d9d9d9;

        &::after {
          background-color: #ffffff;
        }
      }
    }
  }

  &__label {
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
  }

  // 尺寸变体
  &--small {
    .sp-checkbox__input {
      width: 14px;
      height: 14px;

      &:checked::after {
        top: 50%;
        left: 50%;
        width: 3px;
        height: 5px;
        border-width: 1.5px;
        transform: translate(-50%, -60%) rotate(45deg);
      }

      &:indeterminate::after {
        width: 6px;
        height: 1.5px;
      }
    }

    .sp-checkbox__label {
      font-size: 12px;
    }
  }

  &--large {
    .sp-checkbox__input {
      width: 18px;
      height: 18px;

      &:checked::after {
        top: 50%;
        left: 50%;
        width: 5px;
        height: 9px;
        border-width: 2.5px;
        transform: translate(-50%, -60%) rotate(45deg);
      }

      &:indeterminate::after {
        width: 10px;
        height: 2.5px;
      }
    }

    .sp-checkbox__label {
      font-size: 16px;
    }
  }

  // 状态变体
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .sp-checkbox__input,
    .sp-checkbox__label {
      cursor: not-allowed;
    }
  }

  &--checked {
    .sp-checkbox__label {
      color: var(--sp-color-primary, #1890ff);
    }
  }

  &--indeterminate {
    .sp-checkbox__label {
      color: var(--sp-color-primary, #1890ff);
    }
  }

  // 标签位置：JSX 中已经正确处理了标签顺序，不需要额外的 CSS

  // outlined 变体样式
  &--outlined {
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #ffffff;
    transition: all 0.2s ease;

    // 悬停效果
    &:hover {
      border-color: var(--sp-color-primary, #1890ff);
      background-color: #f0f9ff;
    }

    // 选中状态
    &.sp-checkbox--checked {
      border-color: var(--sp-color-primary, #1890ff);
      background-color: #f0f9ff;
    }

    // 禁用状态
    &.sp-checkbox--disabled {
      border-color: #d9d9d9;
      background-color: #f5f5f5;
      cursor: not-allowed;

      &:hover {
        border-color: #d9d9d9;
        background-color: #f5f5f5;
      }
    }

    .sp-checkbox__input {
      // outlined 变体中的复选框输入框保持原有样式
      // 但可以调整一些细节以适应边框容器
    }

    .sp-checkbox__label {
      // outlined 变体中的标签样式
      font-weight: 500;
    }
  }
}

// 复选框组样式
.sp-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }

  &--inline {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }
}
