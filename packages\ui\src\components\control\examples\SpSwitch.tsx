/**
 * SpSwitch - 基于 SpControl 的开关组件示例
 * 
 * 展示如何使用 SpControl 创建开关控件
 */

import { defineComponent, computed, h } from 'vue'
import type { PropType } from 'vue'
import { SpControl, makeControlProps, useControl } from '../control'

// 开关特有的属性
export interface SpSwitchProps {
  /** 选中时的值 */
  checkedValue?: any
  /** 未选中时的值 */
  uncheckedValue?: any
  /** 选中时的颜色 */
  checkedColor?: string
  /** 未选中时的颜色 */
  uncheckedColor?: string
  /** 是否显示文本 */
  showText?: boolean
  /** 选中时的文本 */
  checkedText?: string
  /** 未选中时的文本 */
  uncheckedText?: string
  /** 是否显示图标 */
  showIcon?: boolean
  /** 选中时的图标 */
  checkedIcon?: string
  /** 未选中时的图标 */
  uncheckedIcon?: string
}

// 创建开关 props
export const makeSpSwitchProps = () => ({
  ...makeControlProps(),
  checkedValue: {
    default: true
  },
  uncheckedValue: {
    default: false
  },
  checkedColor: String,
  uncheckedColor: String,
  showText: Boolean,
  checkedText: String,
  uncheckedText: String,
  showIcon: Boolean,
  checkedIcon: String,
  uncheckedIcon: String,
})

// SpSwitch 组件
export const SpSwitch = defineComponent({
  name: 'SpSwitch',
  
  props: makeSpSwitchProps(),
  
  emits: {
    'update:modelValue': (value: any) => true,
    'change': (value: any) => true,
    'focus': (e: FocusEvent) => true,
    'blur': (e: FocusEvent) => true,
    'click': (e: MouseEvent) => true,
    'mouseenter': () => true,
    'mouseleave': () => true,
    'mousedown': () => true,
    'mouseup': () => true,
    'keydown': (e: KeyboardEvent) => true,
  },

  setup(props, { emit, slots, attrs }) {
    const { state, behavior, styles } = useControl(props, emit)

    // 计算是否选中
    const isChecked = computed(() => {
      return state.value === props.checkedValue
    })

    // 开关特有的样式计算
    const switchClasses = computed(() => [
      ...styles.classes.value,
      'sp-switch',
      {
        'sp-switch--checked': isChecked.value,
        'sp-switch--with-text': props.showText,
        'sp-switch--with-icon': props.showIcon,
      }
    ])

    const switchStyles = computed(() => ({
      ...styles.styles.value,
      '--switch-checked-color': props.checkedColor || 'var(--sp-color-primary)',
      '--switch-unchecked-color': props.uncheckedColor || 'var(--sp-color-gray)',
    }))

    // 开关切换处理
    const handleToggle = () => {
      if (state.disabled || state.readonly) return
      
      const newValue = isChecked.value 
        ? props.uncheckedValue 
        : props.checkedValue
        
      behavior.updateValue(newValue)
    }

    // 点击事件处理
    const handleClick = (e: MouseEvent) => {
      handleToggle()
      emit('click', e)
    }

    // 键盘事件处理
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleToggle()
      }
      behavior.handleKeydown(e)
    }

    // 渲染图标
    const renderIcon = (iconName?: string) => {
      if (!iconName) return null
      
      return h('span', {
        class: 'sp-switch__icon',
        innerHTML: `<i class="icon-${iconName}"></i>`
      })
    }

    // 渲染文本
    const renderText = () => {
      if (!props.showText) return null
      
      const text = isChecked.value ? props.checkedText : props.uncheckedText
      if (!text) return null
      
      return h('span', {
        class: 'sp-switch__text'
      }, text)
    }

    // 渲染滑块内容
    const renderThumbContent = () => {
      const content = []
      
      // 图标
      if (props.showIcon) {
        const iconName = isChecked.value ? props.checkedIcon : props.uncheckedIcon
        if (iconName) {
          content.push(renderIcon(iconName))
        }
      }
      
      return content
    }

    return () => h('div', {
      class: switchClasses.value,
      style: switchStyles.value,
      tabindex: state.disabled ? -1 : props.tabindex,
      role: props.role || 'switch',
      'aria-label': props.ariaLabel,
      'aria-describedby': props.ariaDescribedby,
      'aria-disabled': state.disabled,
      'aria-checked': isChecked.value,
      'aria-readonly': state.readonly,
      onClick: handleClick,
      onFocus: behavior.handleFocus,
      onBlur: behavior.handleBlur,
      onMouseenter: behavior.handleMouseenter,
      onMouseleave: behavior.handleMouseleave,
      onKeydown: handleKeydown,
      ...attrs,
    }, [
      // 开关轨道
      h('div', {
        class: 'sp-switch__track'
      }, [
        // 开关滑块
        h('div', {
          class: 'sp-switch__thumb'
        }, renderThumbContent()),
      ]),
      
      // 文本标签
      renderText(),
      
      // 默认插槽
      slots.default?.(),
    ])
  },
})

export default SpSwitch
