import { defineComponent, ref, computed, provide, h } from 'vue'
import Icon from '../icon/Icon.vue'
import { useBtnState } from './useBtnState'
import { btnContext } from '../../composables/useContext'

interface Props {
  variant?: 'default' | 'outlined' | 'text' | 'underline'
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  loadingIcon?: string
  isHovered?: boolean
  isPressed?: boolean
  rounded?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10
  circle?: boolean
  icon?: string
}

export default defineComponent({
  name: 'Btn',
  emits: {
    click: (_event?: MouseEvent) => true,
    mouseenter: () => true,
    mouseleave: () => true,
    mousedown: () => true,
    mouseup: () => true,
  },
  props: {
    variant: {
      type: String as () => Props['variant'],
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String as () => Props['size'],
      default: 'medium',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    loadingIcon: {
      type: String,
      default: 'Refresh',
    },
    isHovered: {
      type: Boolean,
      default: false,
    },
    isPressed: {
      type: Boolean,
      default: false,
    },
    rounded: {
      type: Number as () => Props['rounded'],
      default: undefined,
      validator: (value: number) => value >= 0 && value <= 10,
    },
    circle: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: undefined,
    },
  },
  setup(props, { slots, attrs, emit }) {
    // 确保 disabled 有明确的值
    const isDisabled = computed(
      () => props.disabled === true || props.loading === true
    )

    // 内部状态管理（合并自 btn-base.vue）
    const internalIsHovered = ref(false)
    const internalIsPressed = ref(false)

    // 获取逻辑层组件的引用（保持向后兼容）
    const btnBaseRef = ref()

    // 事件处理逻辑（合并自 useBaseEvents）
    const getDisabled = () => isDisabled.value

    const eventHandlers = {
      click: (event: MouseEvent) => {
        if (!getDisabled()) {
          emit('click', event)
        }
      },

      mouseenter: () => {
        if (!getDisabled()) {
          internalIsHovered.value = true
          emit('mouseenter')
        }
      },

      mouseleave: () => {
        internalIsHovered.value = false
        internalIsPressed.value = false
        if (!getDisabled()) {
          emit('mouseleave')
        }
      },

      mousedown: () => {
        if (!getDisabled()) {
          internalIsPressed.value = true
          emit('mousedown')
        }
      },

      mouseup: () => {
        internalIsPressed.value = false
        if (!getDisabled()) {
          emit('mouseup')
        }
      },
    }

    // 使用按钮状态管理 composable
    const {
      buttonClasses,
      buttonStyles,
      currentIsHovered,
      currentIsPressed,
      bem,
    } = useBtnState({
      props: computed(() => ({
        variant: props.variant || 'default',
        disabled: isDisabled.value,
        size: props.size || 'medium',
        isHovered: props.isHovered || internalIsHovered.value,
        isPressed: props.isPressed || internalIsPressed.value,
        rounded: props.rounded,
        circle: props.circle,
      })),
      btnBaseRef,
    })

    // 计算 loading 图标大小
    const loadingIconSize = computed(() => {
      switch (props.size) {
        case 'small':
          return 14
        case 'large':
          return 18
        default:
          return 16
      }
    })

    // 通过 provide 向上层提供状态（保持向后兼容）
    provide('btnState', {
      isHovered: internalIsHovered,
      isPressed: internalIsPressed,
      disabled: isDisabled,
    })

    // 创建并提供按钮上下文（使用通用上下文）
    const context = btnContext.createStateContext({
      isHovered: currentIsHovered,
      isPressed: currentIsPressed,
      disabled: isDisabled,
      loading: computed(() => props.loading || false),
      variant: props.variant || 'default',
      size: props.size || 'medium',
    })

    btnContext.provideContext(context)

    // 计算要显示的内容
    const renderContent = () => {
      // 优先级：默认插槽 > icon插槽 > icon属性
      if (slots.default) {
        return slots.default()
      }

      if (slots.icon) {
        return slots.icon()
      }

      if (props.icon) {
        return h(Icon, {
          name: props.icon,
          size: loadingIconSize.value,
        })
      }

      return null
    }

    return () => (
      <button
        class={buttonClasses.value}
        style={buttonStyles.value}
        disabled={isDisabled.value}
        ref={btnBaseRef}
        onClick={eventHandlers.click}
        onMouseenter={eventHandlers.mouseenter}
        onMouseleave={eventHandlers.mouseleave}
        onMousedown={eventHandlers.mousedown}
        onMouseup={eventHandlers.mouseup}
        {...attrs}
      >
        {/* Loading 图标 */}
        {props.loading && (
          <span class={[bem.e('loading'), 'btn-loading-icon']}>
            {h(Icon, {
              name: props.loadingIcon || 'Refresh',
              size: loadingIconSize.value,
            })}
          </span>
        )}

        {/* 圆形按钮或普通按钮的内容 */}
        {!props.loading &&
          (() => {
            if (props.circle) {
              // 圆形按钮：只显示图标内容，不显示前置/后置插槽
              const content = renderContent()
              return content ? (
                <span class={bem.e('icon')}>{content}</span>
              ) : null
            } else {
              // 普通按钮：显示前置插槽、主要内容、后置插槽
              return (
                <>
                  {/* 前置插槽 */}
                  {(slots.prepend || slots['prepend-icon']) && (
                    <span class={bem.e('prepend')}>
                      {slots.prepend?.() || slots['prepend-icon']?.()}
                    </span>
                  )}

                  {/* 主要内容 */}
                  {renderContent() && (
                    <span class={bem.e('content')}>{renderContent()}</span>
                  )}

                  {/* 后置插槽 */}
                  {(slots.append || slots['append-icon']) && (
                    <span class={bem.e('append')}>
                      {slots.append?.() || slots['append-icon']?.()}
                    </span>
                  )}
                </>
              )
            }
          })()}
      </button>
    )
  },
})
