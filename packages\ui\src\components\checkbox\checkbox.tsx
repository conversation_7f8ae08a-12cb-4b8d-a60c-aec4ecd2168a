/**
 * SpCheckbox 复选框组件
 *
 * 基于 SpControl 的 checkbox 类型输入控件
 */

import { defineComponent, computed } from 'vue'
import type { PropType } from 'vue'
import { SpControl, makeControlProps } from '../control/control'
import { propsFactory } from '../../utils/propsFactory'

// 类型断言，解决 TSX 中的组件类型问题
const Control = SpControl as any

// 复选框特有的属性
export interface SpCheckboxProps {
  /** 复选框的值（选中时的值） */
  value?: any
  /** 标签文本 */
  label?: string
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'left' | 'right'
  /** 自定义颜色 */
  color?: string
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否为不确定状态 */
  indeterminate?: boolean
}

// 创建复选框 props
export const makeSpCheckboxProps = propsFactory(
  {
    ...makeControlProps(),
    type: {
      type: String,
      default: 'checkbox',
    },
    value: {
      default: true, // 复选框默认选中值为 true
    },
    label: String,
    showLabel: {
      type: Boolean,
      default: true,
    },
    labelPosition: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
    color: String,
    indeterminate: Boolean,
  },
  'SpCheckbox'
)

// SpCheckbox 组件
export const SpCheckbox = defineComponent({
  name: 'SpCheckbox',

  props: makeSpCheckboxProps(),

  emits: {
    'update:modelValue': (value: any) => true,
    change: (e: Event) => true,
    input: (e: Event) => true,
    focus: (e: FocusEvent) => true,
    blur: (e: FocusEvent) => true,
    click: (e: MouseEvent) => true,
  },

  setup(props, { emit, slots, attrs }) {
    // 计算是否选中
    const isChecked = computed(() => {
      if (Array.isArray(props.modelValue)) {
        // 如果 modelValue 是数组，检查是否包含当前值
        return props.modelValue.includes(props.value)
      }
      // 如果 modelValue 不是数组，直接比较
      return !!props.modelValue
    })

    // 复选框样式类
    const checkboxClasses = computed(() => [
      'sp-checkbox',
      `sp-checkbox--${props.size}`,
      {
        'sp-checkbox--checked': isChecked.value,
        'sp-checkbox--disabled': props.disabled,
        'sp-checkbox--indeterminate': props.indeterminate,
        'sp-checkbox--label-left': props.labelPosition === 'left',
      },
    ])

    // 复选框样式
    const checkboxStyles = computed(() => {
      const styles: Record<string, string> = {}

      if (props.color) {
        styles['--checkbox-color'] = props.color
      }

      return styles
    })

    return () => {
      // 渲染标签内容
      const labelContent = props.showLabel
        ? props.label || slots.default?.()
        : null

      // 如果没有标签，只返回复选框
      if (!labelContent) {
        return (
          <Control
            {...props}
            type="checkbox"
            modelValue={props.modelValue}
            trueValue={props.value}
            falseValue={Array.isArray(props.modelValue) ? [] : false}
            class="sp-checkbox__input"
            onUpdate:modelValue={(value: any) =>
              emit('update:modelValue', value)
            }
            onChange={(e: Event) => emit('change', e)}
            onInput={(e: Event) => emit('input', e)}
            onFocus={(e: FocusEvent) => emit('focus', e)}
            onBlur={(e: FocusEvent) => emit('blur', e)}
            onClick={(e: MouseEvent) => emit('click', e)}
            {...attrs}
          />
        )
      }

      // 有标签的情况
      return (
        <label
          class={checkboxClasses.value}
          style={checkboxStyles.value}
        >
          {props.labelPosition === 'left' && (
            <span class="sp-checkbox__label">{labelContent}</span>
          )}

          <Control
            {...props}
            type="checkbox"
            modelValue={props.modelValue}
            trueValue={props.value}
            falseValue={Array.isArray(props.modelValue) ? [] : false}
            class="sp-checkbox__input"
            onUpdate:modelValue={(value: any) =>
              emit('update:modelValue', value)
            }
            onChange={(e: Event) => emit('change', e)}
            onInput={(e: Event) => emit('input', e)}
            onFocus={(e: FocusEvent) => emit('focus', e)}
            onBlur={(e: FocusEvent) => emit('blur', e)}
            onClick={(e: MouseEvent) => emit('click', e)}
            {...attrs}
          />

          {props.labelPosition === 'right' && (
            <span class="sp-checkbox__label">{labelContent}</span>
          )}
        </label>
      )
    }
  },
})

export default SpCheckbox
