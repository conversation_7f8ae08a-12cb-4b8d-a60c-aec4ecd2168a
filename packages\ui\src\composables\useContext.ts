/**
 * 通用组件上下文管理 composable
 * 提供统一的组件状态管理和通信机制
 */

import { provide, inject, type InjectionKey, type Ref, computed } from 'vue'

// 基础上下文接口
export interface BaseContext {
  [key: string]: any
}

// 上下文配置选项
export interface ContextOptions<T extends BaseContext> {
  /** 上下文名称，用于生成唯一的注入键 */
  name: string
  /** 默认值 */
  defaultValue?: T | null
  /** 是否在找不到上下文时抛出错误 */
  strict?: boolean
}

/**
 * 创建上下文管理器
 * @param options 配置选项
 * @returns 上下文管理器对象
 */
export function createContext<T extends BaseContext>(options: ContextOptions<T>) {
  const { name, defaultValue = null, strict = false } = options
  
  // 创建唯一的注入键
  const contextKey: InjectionKey<T> = Symbol(`${name}-context`)
  
  /**
   * 提供上下文
   * @param context 上下文数据
   * @returns 提供的上下文
   */
  const provideContext = (context: T): T => {
    provide(contextKey, context)
    return context
  }
  
  /**
   * 注入上下文
   * @returns 上下文数据和相关信息
   */
  const useContext = () => {
    const context = inject(contextKey, defaultValue)
    
    if (strict && !context) {
      throw new Error(`useContext must be used within a ${name} provider`)
    }
    
    return {
      context,
      hasContext: !!context,
    }
  }
  
  /**
   * 创建上下文数据
   * @param data 初始数据
   * @returns 上下文对象
   */
  const createContextData = (data: T): T => {
    return data
  }
  
  return {
    contextKey,
    provideContext,
    useContext,
    createContextData,
  }
}

/**
 * 创建组件状态上下文
 * 专门用于管理组件的交互状态
 */
export interface ComponentStateContext {
  isHovered: Ref<boolean>
  isPressed: Ref<boolean>
  isFocused: Ref<boolean>
  isActive: Ref<boolean>
  disabled: Ref<boolean>
  loading: Ref<boolean>
  variant?: string
  size?: string
  updateState?: (state: Partial<{
    isHovered: boolean
    isPressed: boolean
    isFocused: boolean
    isActive: boolean
    disabled: boolean
    loading: boolean
  }>) => void
}

/**
 * 创建组件状态上下文管理器
 * @param name 组件名称
 * @returns 状态上下文管理器
 */
export function createComponentStateContext(name: string) {
  const { provideContext, useContext, createContextData } = createContext<ComponentStateContext>({
    name,
    strict: false,
  })
  
  /**
   * 创建组件状态上下文数据
   */
  const createStateContext = (options: {
    isHovered: Ref<boolean>
    isPressed: Ref<boolean>
    isFocused?: Ref<boolean>
    isActive?: Ref<boolean>
    disabled: Ref<boolean>
    loading?: Ref<boolean>
    variant?: string
    size?: string
  }): ComponentStateContext => {
    const {
      isHovered,
      isPressed,
      isFocused = computed(() => false),
      isActive = computed(() => false),
      disabled,
      loading = computed(() => false),
      variant,
      size,
    } = options
    
    const updateState = (state: Partial<{
      isHovered: boolean
      isPressed: boolean
      isFocused: boolean
      isActive: boolean
      disabled: boolean
      loading: boolean
    }>) => {
      if (state.isHovered !== undefined) {
        isHovered.value = state.isHovered
      }
      if (state.isPressed !== undefined) {
        isPressed.value = state.isPressed
      }
      if (state.isFocused !== undefined && 'value' in isFocused) {
        (isFocused as any).value = state.isFocused
      }
      if (state.isActive !== undefined && 'value' in isActive) {
        (isActive as any).value = state.isActive
      }
      if (state.disabled !== undefined) {
        disabled.value = state.disabled
      }
      if (state.loading !== undefined && 'value' in loading) {
        (loading as any).value = state.loading
      }
    }
    
    return createContextData({
      isHovered,
      isPressed,
      isFocused,
      isActive,
      disabled,
      loading,
      variant,
      size,
      updateState,
    })
  }
  
  return {
    provideContext,
    useContext,
    createStateContext,
  }
}

/**
 * 预定义的常用上下文管理器
 */

// 按钮上下文
export const btnContext = createComponentStateContext('btn')

// 输入框上下文
export const inputContext = createComponentStateContext('input')

// 表单项上下文
export const formItemContext = createComponentStateContext('form-item')

// 菜单上下文
export const menuContext = createComponentStateContext('menu')

// 表格上下文
export const tableContext = createComponentStateContext('table')

/**
 * 快速创建简单上下文
 * @param name 上下文名称
 * @param defaultValue 默认值
 * @returns 简单上下文管理器
 */
export function createSimpleContext<T = any>(name: string, defaultValue?: T) {
  return createContext<T>({
    name,
    defaultValue,
    strict: false,
  })
}
