/**
 * SpRadio 单选按钮组件
 *
 * 基于 SpControl 的 radio 类型输入控件
 */

import { defineComponent, computed } from 'vue'
import type { PropType } from 'vue'
import { SpControl, makeControlProps } from '../control/control'
import { propsFactory } from '../../utils/propsFactory'

// 类型断言，解决 TSX 中的组件类型问题
const Control = SpControl as any

// 单选按钮特有的属性
export interface SpRadioProps {
  /** 单选按钮的值 */
  value: any
  /** 标签文本 */
  label?: string
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'left' | 'right'
  /** 自定义颜色 */
  color?: string
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large'
}

// 创建单选按钮 props
export const makeSpRadioProps = propsFactory(
  {
    ...makeControlProps(),
    type: {
      type: String,
      default: 'radio',
    },
    value: {
      required: true,
    },
    label: String,
    showLabel: {
      type: Boolean,
      default: true,
    },
    labelPosition: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
    color: String,
  },
  'SpRadio'
)

// SpRadio 组件
export const SpRadio = defineComponent({
  name: 'SpRadio',

  props: makeSpRadioProps(),

  emits: {
    'update:modelValue': (value: any) => true,
    change: (e: Event) => true,
    input: (e: Event) => true,
    focus: (e: FocusEvent) => true,
    blur: (e: FocusEvent) => true,
    click: (e: MouseEvent) => true,
  },

  setup(props, { emit, slots, attrs }) {
    // 计算是否选中
    const isChecked = computed(() => {
      return props.modelValue === props.value
    })

    // 单选按钮样式类
    const radioClasses = computed(() => [
      'sp-radio',
      `sp-radio--${props.size}`,
      {
        'sp-radio--checked': isChecked.value,
        'sp-radio--disabled': props.disabled,
        'sp-radio--label-left': props.labelPosition === 'left',
      },
    ])

    // 单选按钮样式
    const radioStyles = computed(() => {
      const styles: Record<string, string> = {}

      if (props.color) {
        styles['--radio-color'] = props.color
      }

      return styles
    })

    return () => {
      // 渲染标签内容
      const labelContent = props.showLabel
        ? props.label || slots.default?.()
        : null

      // 如果没有标签，只返回单选按钮
      if (!labelContent) {
        return (
          <Control
            {...props}
            type="radio"
            modelValue={props.modelValue}
            trueValue={props.value}
            falseValue={undefined}
            class="sp-radio__input"
            onUpdate:modelValue={(value: any) =>
              emit('update:modelValue', value)
            }
            onChange={(e: Event) => emit('change', e)}
            onInput={(e: Event) => emit('input', e)}
            onFocus={(e: FocusEvent) => emit('focus', e)}
            onBlur={(e: FocusEvent) => emit('blur', e)}
            onClick={(e: MouseEvent) => emit('click', e)}
            {...attrs}
          />
        )
      }

      // 有标签的情况
      return (
        <label
          class={radioClasses.value}
          style={radioStyles.value}
        >
          {props.labelPosition === 'left' && (
            <span class="sp-radio__label">{labelContent}</span>
          )}

          <Control
            {...props}
            type="radio"
            modelValue={props.modelValue}
            trueValue={props.value}
            falseValue={undefined}
            class="sp-radio__input"
            onUpdate:modelValue={(value: any) =>
              emit('update:modelValue', value)
            }
            onChange={(e: Event) => emit('change', e)}
            onInput={(e: Event) => emit('input', e)}
            onFocus={(e: FocusEvent) => emit('focus', e)}
            onBlur={(e: FocusEvent) => emit('blur', e)}
            onClick={(e: MouseEvent) => emit('click', e)}
            {...attrs}
          />

          {props.labelPosition === 'right' && (
            <span class="sp-radio__label">{labelContent}</span>
          )}
        </label>
      )
    }
  },
})

export default SpRadio
