/**
 * Btn 组件相关类型定义
 */

// 按钮变体类型
export type BtnVariant = 'default' | 'outlined' | 'text' | 'underline'

// 按钮尺寸类型
export type BtnSize = 'small' | 'medium' | 'large'

// 按钮属性接口（继承样式层属性）
export interface BtnProps extends BtnStyleProps {}

// 按钮事件接口
export interface BtnEmits {
  /**
   * 点击事件
   */
  click: [event?: MouseEvent]
  /**
   * 鼠标进入事件
   */
  mouseenter: []
  /**
   * 鼠标离开事件
   */
  mouseleave: []
  /**
   * 鼠标按下事件
   */
  mousedown: []
  /**
   * 鼠标释放事件
   */
  mouseup: []
}

// 按钮基础属性接口（逻辑层）
export interface BtnBaseProps {
  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean
}

// 按钮圆角类型
export type BtnRounded = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10

// 按钮样式属性接口（样式层）
export interface BtnStyleProps {
  /**
   * 按钮变体
   * @default 'default'
   */
  variant?: BtnVariant

  /**
   * 按钮尺寸
   * @default 'medium'
   */
  size?: BtnSize

  /**
   * 是否禁用
   * @default false
   */
  disabled?: boolean

  /**
   * 是否悬停状态
   */
  isHovered?: boolean

  /**
   * 是否按下状态
   */
  isPressed?: boolean

  /**
   * 圆角大小 (0-10)
   * @default undefined
   */
  rounded?: BtnRounded

  /**
   * 是否为圆形按钮
   * @default false
   */
  circle?: boolean

  /**
   * 图标名称（当没有默认插槽和icon插槽时显示）
   * @default undefined
   */
  icon?: string
}

// 按钮实例类型
export interface BtnInstance {
  // 暂时没有暴露的方法，预留接口
}

// 按钮状态注入类型
export interface BtnStateInject {
  isHovered: import('vue').Ref<boolean>
  isPressed: import('vue').Ref<boolean>
  disabled: import('vue').Ref<boolean>
}

// 按钮组方向类型
export type BtnGroupDirection = 'horizontal' | 'vertical'

// 按钮组属性接口
export interface BtnGroupProps {
  /**
   * 按钮组排列方向
   * @default 'horizontal'
   */
  direction?: BtnGroupDirection

  /**
   * 按钮组尺寸
   * @default 'medium'
   */
  size?: BtnSize

  /**
   * 按钮组变体
   * @default 'default'
   */
  variant?: BtnVariant

  /**
   * 是否禁用整个按钮组
   * @default false
   */
  disabled?: boolean

  /**
   * 按钮之间的间距（像素）
   * @default 0
   */
  gap?: number
}

// 按钮组实例类型
export interface BtnGroupInstance {
  // 预留接口
}
