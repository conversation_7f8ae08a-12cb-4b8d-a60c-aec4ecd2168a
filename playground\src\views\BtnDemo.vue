<template>
  <div class="btn-demo">
    <div class="demo-header">
      <h1>🎯 Btn 按钮组件演示</h1>
      <p>展示新的按钮组件的各种变体、插槽功能和按钮组</p>
    </div>

    <!-- 基础变体 -->
    <section class="demo-section">
      <h2>📦 基础变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Default 默认按钮</h3>
          <sp-btn
            variant="default"
            @click="handleClick('default')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Outlined 边框按钮</h3>
          <sp-btn
            variant="outlined"
            @click="handleClick('outlined')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Text 文本按钮</h3>
          <sp-btn
            variant="text"
            @click="handleClick('text')"
          >
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Underline 下划线按钮</h3>
          <sp-btn
            variant="underline"
            @click="handleClick('underline')"
          >
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 前置和后置插槽 -->
    <section class="demo-section">
      <h2>🎨 前置和后置插槽</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>前置图标</h3>
          <sp-btn @click="handleClick('prepend-icon')">
            <template #prepend-icon>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                />
              </svg>
            </template>
            收藏
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>后置图标</h3>
          <sp-btn
            variant="outlined"
            @click="handleClick('append-icon')"
          >
            下一步
            <template #append-icon>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                />
              </svg>
            </template>
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>只有图标</h3>
          <sp-btn @click="handleClick('icon-only')">
            <template #prepend-icon>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                />
              </svg>
            </template>
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>前置+后置</h3>
          <sp-btn @click="handleClick('both-icons')">
            <template #prepend-icon>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
              </svg>
            </template>
            完成
            <template #append-icon>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                />
              </svg>
            </template>
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>Small 小尺寸</h3>
          <sp-btn
            variant="default"
            size="small"
            @click="handleClick('small')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Medium 中等尺寸</h3>
          <sp-btn
            variant="default"
            size="medium"
            @click="handleClick('medium')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>Large 大尺寸</h3>
          <sp-btn
            variant="default"
            size="large"
            @click="handleClick('large')"
          >
            <template #prepend-icon>📝</template>
            BUTTON
          </sp-btn>
        </div>
      </div>
    </section>

    <!-- 圆角变体 -->
    <section class="demo-section">
      <h2>🔘 圆角变体 (Rounded)</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>圆角数值演示</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              :rounded="0"
              @click="handleClick('rounded-0')"
            >
              Rounded 0
            </sp-btn>
            <sp-btn
              variant="default"
              :rounded="2"
              @click="handleClick('rounded-2')"
            >
              Rounded 2
            </sp-btn>
            <sp-btn
              variant="default"
              :rounded="5"
              @click="handleClick('rounded-5')"
            >
              Rounded 5
            </sp-btn>
            <sp-btn
              variant="default"
              :rounded="10"
              @click="handleClick('rounded-10')"
            >
              Rounded 10
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>不同变体的圆角</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              :rounded="3"
              @click="handleClick('default-rounded')"
            >
              Default
            </sp-btn>
            <sp-btn
              variant="outlined"
              :rounded="3"
              @click="handleClick('outlined-rounded')"
            >
              Outlined
            </sp-btn>
            <sp-btn
              variant="text"
              :rounded="3"
              @click="handleClick('text-rounded')"
            >
              Text
            </sp-btn>
            <sp-btn
              variant="underline"
              :rounded="3"
              @click="handleClick('underline-rounded')"
            >
              Underline
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>不同尺寸的圆角</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              size="small"
              :rounded="4"
              @click="handleClick('small-rounded')"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="default"
              size="medium"
              :rounded="4"
              @click="handleClick('medium-rounded')"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="default"
              size="large"
              :rounded="4"
              @click="handleClick('large-rounded')"
            >
              Large
            </sp-btn>
          </div>
        </div>
      </div>

      <div class="demo-row">
        <div class="demo-item">
          <h3>极端圆角值</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="outlined"
              :rounded="0"
              @click="handleClick('square')"
            >
              <template #prepend-icon>⬜</template>
              方形 (0)
            </sp-btn>
            <sp-btn
              variant="outlined"
              :rounded="10"
              @click="handleClick('pill')"
            >
              <template #prepend-icon>💊</template>
              胶囊 (10)
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>带图标的圆角按钮</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              :rounded="6"
              @click="handleClick('icon-rounded-1')"
            >
              <template #prepend-icon>⭐</template>
              收藏
            </sp-btn>
            <sp-btn
              variant="outlined"
              :rounded="8"
              @click="handleClick('icon-rounded-2')"
            >
              下载
              <template #append-icon>⬇️</template>
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>圆角说明</h3>
          <div class="rounded-info">
            <ul>
              <li>
                <strong>0</strong>
                : 完全方形，无圆角
              </li>
              <li>
                <strong>1-3</strong>
                : 轻微圆角，适合现代设计
              </li>
              <li>
                <strong>4-6</strong>
                : 中等圆角，平衡美观
              </li>
              <li>
                <strong>7-9</strong>
                : 较大圆角，柔和感强
              </li>
              <li>
                <strong>10</strong>
                : 最大圆角，胶囊形状
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 圆形按钮 -->
    <section class="demo-section">
      <h2>⭕ 圆形按钮 (Circle)</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>基础圆形按钮</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              icon="Add"
              @click="handleClick('circle-add')"
            />
            <sp-btn
              circle
              variant="outlined"
              icon="Search"
              @click="handleClick('circle-search')"
            />
            <sp-btn
              circle
              variant="text"
              icon="Settings"
              @click="handleClick('circle-settings')"
            />
          </div>
        </div>

        <div class="demo-item">
          <h3>使用 icon 插槽</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              @click="handleClick('circle-icon-slot-1')"
            >
              <template #icon>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                  />
                </svg>
              </template>
            </sp-btn>
            <sp-btn
              circle
              variant="outlined"
              @click="handleClick('circle-icon-slot-2')"
            >
              <template #icon>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
              </template>
            </sp-btn>
            <sp-btn
              circle
              variant="text"
              @click="handleClick('circle-icon-slot-3')"
            >
              <template #icon>❤️</template>
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>文字覆盖图标</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              icon="Settings"
              @click="handleClick('circle-text-1')"
            >
              A
            </sp-btn>
            <sp-btn
              circle
              variant="outlined"
              @click="handleClick('circle-text-2')"
            >
              <template #icon>⚙️</template>
              B
            </sp-btn>
            <sp-btn
              circle
              variant="text"
              icon="Home"
              @click="handleClick('circle-text-3')"
            >
              C
            </sp-btn>
          </div>
        </div>
      </div>

      <div class="demo-row">
        <div class="demo-item">
          <h3>不同尺寸</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              size="small"
              icon="Edit"
              @click="handleClick('circle-small')"
            />
            <sp-btn
              circle
              size="medium"
              icon="Edit"
              @click="handleClick('circle-medium')"
            />
            <sp-btn
              circle
              size="large"
              icon="Edit"
              @click="handleClick('circle-large')"
            />
          </div>
        </div>

        <div class="demo-item">
          <h3>不同变体</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              variant="default"
              icon="Download"
              @click="handleClick('circle-default')"
            />
            <sp-btn
              circle
              variant="outlined"
              icon="Download"
              @click="handleClick('circle-outlined')"
            />
            <sp-btn
              circle
              variant="text"
              icon="Download"
              @click="handleClick('circle-text')"
            />
          </div>
        </div>

        <div class="demo-item">
          <h3>状态演示</h3>
          <div class="demo-buttons">
            <sp-btn
              circle
              icon="Check"
              @click="handleClick('circle-normal')"
            />
            <sp-btn
              circle
              icon="Check"
              disabled
              @click="handleClick('circle-disabled')"
            />
            <sp-btn
              circle
              icon="Refresh"
              :loading="circleLoading"
              @click="toggleCircleLoading"
            />
          </div>
        </div>
      </div>

      <div class="demo-row">
        <div class="demo-item">
          <h3>优先级演示</h3>
          <div class="priority-demo">
            <div class="priority-item">
              <h4>1. 只有 icon 属性</h4>
              <sp-btn
                circle
                icon="Home"
                @click="handleClick('priority-1')"
              />
            </div>
            <div class="priority-item">
              <h4>2. icon 属性 + icon 插槽</h4>
              <sp-btn
                circle
                icon="Home"
                @click="handleClick('priority-2')"
              >
                <template #icon>⭐</template>
              </sp-btn>
            </div>
            <div class="priority-item">
              <h4>3. icon 属性 + icon 插槽 + 默认插槽</h4>
              <sp-btn
                circle
                icon="Home"
                @click="handleClick('priority-3')"
              >
                <template #icon>⭐</template>
                A
              </sp-btn>
            </div>
          </div>
        </div>

        <div class="demo-item">
          <h3>圆形按钮说明</h3>
          <div class="circle-info">
            <ul>
              <li>
                <strong>circle</strong>
                : 设置为圆形按钮
              </li>
              <li>
                <strong>icon</strong>
                : 图标名称属性
              </li>
              <li>
                <strong>#icon</strong>
                : 图标插槽
              </li>
              <li>
                <strong>优先级</strong>
                : 默认插槽 > #icon 插槽 > icon 属性
              </li>
              <li>
                <strong>尺寸</strong>
                : 自动调整为正方形
              </li>
              <li>
                <strong>变体</strong>
                : 支持所有按钮变体
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 按钮组 - 水平 -->
    <section class="demo-section">
      <h2>🔗 按钮组 - 水平排列</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>基础按钮组</h3>
          <sp-btn-group direction="horizontal">
            <sp-btn @click="handleClick('group-left')">左侧</sp-btn>
            <sp-btn @click="handleClick('group-center')">中间</sp-btn>
            <sp-btn @click="handleClick('group-right')">右侧</sp-btn>
          </sp-btn-group>
        </div>

        <div class="demo-item">
          <h3>带图标的按钮组</h3>
          <sp-btn-group
            direction="horizontal"
            variant="outlined"
          >
            <sp-btn @click="handleClick('group-edit')">
              <template #prepend-icon>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                  />
                </svg>
              </template>
              编辑
            </sp-btn>
            <sp-btn @click="handleClick('group-delete')">
              <template #prepend-icon>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                  />
                </svg>
              </template>
              删除
            </sp-btn>
            <sp-btn @click="handleClick('group-confirm')">
              <template #prepend-icon>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                  />
                </svg>
              </template>
              确认
            </sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- 按钮组 - 垂直 -->
    <section class="demo-section">
      <h2>📐 按钮组 - 垂直排列</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>垂直按钮组</h3>
          <sp-btn-group direction="vertical">
            <sp-btn @click="handleClick('vertical-up')">
              <template #prepend-icon>⬆️</template>
              向上
            </sp-btn>
            <sp-btn @click="handleClick('vertical-center')">
              <template #prepend-icon>🔄</template>
              中间
            </sp-btn>
            <sp-btn @click="handleClick('vertical-down')">
              <template #prepend-icon>⬇️</template>
              向下
            </sp-btn>
          </sp-btn-group>
        </div>

        <div class="demo-item">
          <h3>文本变体组</h3>
          <sp-btn-group
            direction="vertical"
            variant="text"
          >
            <sp-btn @click="handleClick('text-1')">文本按钮1</sp-btn>
            <sp-btn @click="handleClick('text-2')">文本按钮2</sp-btn>
            <sp-btn @click="handleClick('text-3')">文本按钮3</sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🎭 状态演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>正常状态</h3>
          <sp-btn
            variant="default"
            @click="handleClick('normal')"
          >
            <template #prepend-icon>🚫</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>禁用按钮</h3>
          <sp-btn
            variant="default"
            disabled
            @click="handleClick('disabled')"
          >
            <template #prepend-icon>🚫</template>
            BUTTON
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>禁用按钮组</h3>
          <sp-btn-group disabled>
            <sp-btn @click="handleClick('group-disabled-1')">禁用组1</sp-btn>
            <sp-btn @click="handleClick('group-disabled-2')">禁用组2</sp-btn>
            <sp-btn @click="handleClick('group-disabled-3')">禁用组3</sp-btn>
          </sp-btn-group>
        </div>
      </div>
    </section>

    <!-- Underline 变体专门演示 -->
    <section class="demo-section">
      <h2>📝 Underline 下划线变体演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>基础下划线按钮</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              @click="handleClick('underline-basic')"
            >
              链接样式
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-nav')"
            >
              导航链接
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-action')"
            >
              操作链接
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>带图标的下划线按钮</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              @click="handleClick('underline-icon-1')"
            >
              <template #prepend-icon>🔗</template>
              外部链接
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-icon-2')"
            >
              查看详情
              <template #append-icon>→</template>
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-icon-3')"
            >
              <template #prepend-icon>📄</template>
              下载文档
              <template #append-icon>⬇</template>
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>不同尺寸下划线按钮</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              size="small"
              @click="handleClick('underline-small')"
            >
              小尺寸链接
            </sp-btn>
            <sp-btn
              variant="underline"
              size="medium"
              @click="handleClick('underline-medium')"
            >
              中等尺寸链接
            </sp-btn>
            <sp-btn
              variant="underline"
              size="large"
              @click="handleClick('underline-large')"
            >
              大尺寸链接
            </sp-btn>
          </div>
        </div>
      </div>

      <div class="demo-row">
        <div class="demo-item">
          <h3>下划线按钮状态</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              @click="handleClick('underline-normal')"
            >
              正常状态
            </sp-btn>
            <sp-btn
              variant="underline"
              disabled
            >
              禁用状态
            </sp-btn>
            <sp-btn
              variant="underline"
              :loading="underlineLoading"
              @click="toggleUnderlineLoading"
            >
              {{ underlineLoading ? '加载中...' : '加载状态' }}
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>使用场景示例</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              @click="handleClick('underline-forgot')"
            >
              忘记密码？
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-terms')"
            >
              用户协议
            </sp-btn>
            <sp-btn
              variant="underline"
              @click="handleClick('underline-more')"
            >
              查看更多
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>下划线特性说明</h3>
          <div class="underline-info">
            <ul>
              <li>默认显示半透明细下划线</li>
              <li>悬停时从中间向两边展开粗下划线</li>
              <li>双层下划线设计，动画更流畅</li>
              <li>适合链接和导航场景</li>
              <li>支持前置和后置图标</li>
              <li>保持文本按钮的轻量感</li>
              <li>提供更好的交互反馈</li>
              <li>禁用状态下划线变淡</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Loading 状态演示 -->
    <section class="demo-section">
      <h2>⏳ Loading 状态演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>基础 Loading</h3>
          <sp-btn
            :loading="loading1"
            @click="toggleLoading1"
          >
            {{ loading1 ? '加载中...' : '点击加载' }}
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>不同变体 Loading</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              :loading="loading2"
              @click="toggleLoading2"
            >
              {{ loading2 ? '处理中...' : '默认按钮' }}
            </sp-btn>
            <sp-btn
              variant="outlined"
              :loading="loading2"
              @click="toggleLoading2"
            >
              {{ loading2 ? '处理中...' : '边框按钮' }}
            </sp-btn>
            <sp-btn
              variant="text"
              :loading="loading2"
              @click="toggleLoading2"
            >
              {{ loading2 ? '处理中...' : '文本按钮' }}
            </sp-btn>
            <sp-btn
              variant="underline"
              :loading="loading2"
              @click="toggleLoading2"
            >
              {{ loading2 ? '处理中...' : '下划线按钮' }}
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>不同尺寸 Loading</h3>
          <div class="demo-buttons">
            <sp-btn
              size="small"
              :loading="loading3"
              @click="toggleLoading3"
            >
              小按钮
            </sp-btn>
            <sp-btn
              size="medium"
              :loading="loading3"
              @click="toggleLoading3"
            >
              中等按钮
            </sp-btn>
            <sp-btn
              size="large"
              :loading="loading3"
              @click="toggleLoading3"
            >
              大按钮
            </sp-btn>
          </div>
        </div>
      </div>

      <div class="demo-row">
        <div class="demo-item">
          <h3>自定义 Loading 图标</h3>
          <div class="demo-buttons">
            <sp-btn
              :loading="loading4"
              loading-icon="Refresh"
              @click="toggleLoading4"
            >
              默认图标
            </sp-btn>
            <sp-btn
              :loading="loading4"
              loading-icon="RefreshOutline"
              @click="toggleLoading4"
            >
              轮廓图标
            </sp-btn>
            <sp-btn
              :loading="loading4"
              loading-icon="Settings"
              @click="toggleLoading4"
            >
              设置图标
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>实际使用场景</h3>
          <div class="demo-buttons">
            <sp-btn
              :loading="submitting"
              @click="handleSubmit"
            >
              {{ submitting ? '提交中...' : '提交订单' }}
            </sp-btn>
            <sp-btn
              variant="outlined"
              :loading="downloading"
              @click="handleDownload"
            >
              {{ downloading ? '下载中...' : '下载文件' }}
            </sp-btn>
            <sp-btn
              variant="text"
              :loading="saving"
              @click="handleSave"
            >
              {{ saving ? '保存中...' : '保存文档' }}
            </sp-btn>
          </div>
        </div>

        <div class="demo-item">
          <h3>Loading 状态说明</h3>
          <div class="loading-info">
            <ul>
              <li>Loading 时按钮自动禁用</li>
              <li>显示旋转的加载图标</li>
              <li>可自定义加载图标</li>
              <li>支持所有变体和尺寸</li>
              <li>Loading 时隐藏前置/后置插槽</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 组合演示 -->
    <section class="demo-section">
      <h2>🎨 组合演示</h2>
      <div class="demo-grid">
        <!-- Default 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Default 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="default"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="default"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Outlined 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Outlined 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="outlined"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="outlined"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Text 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Text 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="text"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="text"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="text"
              size="large"
            >
              Large
            </sp-btn>
          </div>
        </div>

        <!-- Underline 变体的所有尺寸 -->
        <div class="demo-group">
          <h3>Underline 变体</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="underline"
              size="small"
            >
              Small
            </sp-btn>
            <sp-btn
              variant="underline"
              size="medium"
            >
              Medium
            </sp-btn>
            <sp-btn
              variant="underline"
              size="large"
            >
              Medium
            </sp-btn>
          </div>
        </div>

        <!-- 禁用状态 -->
        <div class="demo-group">
          <h3>禁用状态</h3>
          <div class="demo-buttons">
            <sp-btn
              variant="default"
              disabled
            >
              Default
            </sp-btn>
            <sp-btn
              variant="outlined"
              disabled
            >
              Outlined
            </sp-btn>
            <sp-btn
              variant="text"
              disabled
            >
              Text
            </sp-btn>
            <sp-btn
              variant="underline"
              disabled
            >
              Underline
            </sp-btn>
          </div>
        </div>
      </div>
    </section>

    <!-- 交互演示 -->
    <section class="demo-section">
      <h2>🎮 交互演示</h2>
      <div class="demo-row">
        <div class="demo-item">
          <h3>点击事件</h3>
          <sp-btn @click="handleClick('click-demo')">
            <template #prepend-icon>�</template>
            点击我
          </sp-btn>
        </div>

        <div class="demo-item">
          <h3>鼠标事件</h3>
          <sp-btn
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
            @click="handleClick('mouse-demo')"
          >
            <template #prepend-icon>🖱️</template>
            悬停我
          </sp-btn>
        </div>
      </div>
      <p
        v-if="message"
        class="message"
      >
        {{ message }}
      </p>
    </section>

    <!-- 交互说明 -->
    <section class="demo-section">
      <h2>💡 功能特性</h2>
      <div class="interaction-info">
        <ul>
          <li>
            <strong>前置插槽</strong>
            ：使用 #prepend-icon 或 #prepend 插槽添加前置内容
          </li>
          <li>
            <strong>后置插槽</strong>
            ：使用 #append-icon 或 #append 插槽添加后置内容
          </li>
          <li>
            <strong>按钮组</strong>
            ：支持水平和垂直排列，自动处理边框圆角
          </li>
          <li>
            <strong>状态管理</strong>
            ：通过 provide/inject 实现嵌套状态共享
          </li>
          <li>
            <strong>双层架构</strong>
            ：外层负责样式，内层负责逻辑，职责分离
          </li>
        </ul>
      </div>
    </section>

    <!-- 点击日志 -->
    <section class="demo-section">
      <h2>📝 点击日志</h2>
      <div class="click-log">
        <div
          v-if="clickLog.length === 0"
          class="no-clicks"
        >
          暂无点击记录，点击上面的按钮试试！
        </div>
        <div v-else>
          <div
            v-for="(log, index) in clickLog"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">点击了</span>
            <span class="log-target">{{ log.target }}</span>
            <span class="log-button">按钮</span>
          </div>
        </div>
        <button
          v-if="clickLog.length > 0"
          @click="clearLog"
          class="clear-log"
        >
          清空日志
        </button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  // 注意：sp-btn 和 sp-btn-group 组件已经通过 SpeedUI 插件全局注册，无需手动导入

  // 点击日志
  interface ClickLog {
    time: string
    target: string
  }

  const clickLog = ref<ClickLog[]>([])
  const message = ref('')

  // Loading 状态
  const loading1 = ref(false)
  const loading2 = ref(false)
  const loading3 = ref(false)
  const loading4 = ref(false)
  const submitting = ref(false)
  const downloading = ref(false)
  const saving = ref(false)
  const underlineLoading = ref(false)
  const circleLoading = ref(false)

  const handleClick = (target: string) => {
    const now = new Date()
    const time = now.toLocaleTimeString()

    clickLog.value.unshift({
      time,
      target,
    })

    // 限制日志数量
    if (clickLog.value.length > 10) {
      clickLog.value = clickLog.value.slice(0, 10)
    }
  }

  const handleMouseEnter = () => {
    message.value = '鼠标悬停中...'
  }

  const handleMouseLeave = () => {
    message.value = '鼠标离开了'
    setTimeout(() => {
      message.value = ''
    }, 1000)
  }

  const clearLog = () => {
    clickLog.value = []
  }

  // Loading 方法
  const toggleLoading1 = () => {
    loading1.value = true
    setTimeout(() => {
      loading1.value = false
    }, 2000)
  }

  const toggleLoading2 = () => {
    loading2.value = true
    setTimeout(() => {
      loading2.value = false
    }, 3000)
  }

  const toggleLoading3 = () => {
    loading3.value = true
    setTimeout(() => {
      loading3.value = false
    }, 2500)
  }

  const toggleLoading4 = () => {
    loading4.value = true
    setTimeout(() => {
      loading4.value = false
    }, 2000)
  }

  const handleSubmit = () => {
    submitting.value = true
    // 模拟 API 请求
    setTimeout(() => {
      submitting.value = false
      alert('订单提交成功！')
    }, 3000)
  }

  const handleDownload = () => {
    downloading.value = true
    // 模拟文件下载
    setTimeout(() => {
      downloading.value = false
      alert('文件下载完成！')
    }, 4000)
  }

  const handleSave = () => {
    saving.value = true
    // 模拟保存操作
    setTimeout(() => {
      saving.value = false
      alert('文档保存成功！')
    }, 2500)
  }

  const toggleUnderlineLoading = () => {
    underlineLoading.value = true
    setTimeout(() => {
      underlineLoading.value = false
    }, 2000)
  }

  const toggleCircleLoading = () => {
    circleLoading.value = true
    setTimeout(() => {
      circleLoading.value = false
    }, 2000)
  }
</script>

<style scoped>
  .btn-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
  }

  .demo-header h1 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }

  .demo-header p {
    color: #7f8c8d;
    font-size: 1.2rem;
  }

  .demo-section {
    margin-bottom: 3rem;
  }

  .demo-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
    font-size: 1.5rem;
  }

  .demo-row {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .demo-item {
    flex: 1;
    min-width: 200px;
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
    text-align: center;
  }

  .demo-item h3 {
    color: #34495e;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .demo-group {
    padding: 1.5rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-group h3 {
    color: #34495e;
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.1rem;
  }

  .demo-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
  }

  .interaction-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .interaction-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .interaction-info li {
    margin-bottom: 0.5rem;
    color: #495057;
  }

  .click-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 100px;
  }

  .no-clicks {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
  }

  .log-item {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
  }

  .log-time {
    color: #6c757d;
    font-weight: bold;
  }

  .log-action {
    color: #495057;
  }

  .log-target {
    color: #007bff;
    font-weight: bold;
  }

  .log-button {
    color: #495057;
  }

  .clear-log {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .clear-log:hover {
    background: #c82333;
  }

  .message {
    margin-top: 16px;
    padding: 12px;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
    color: #2e7d32;
    font-weight: 500;
    text-align: center;
  }

  .loading-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .loading-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .loading-info li {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.9rem;
  }

  .underline-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .underline-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .underline-info li {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.9rem;
  }

  .rounded-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .rounded-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .rounded-info li {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.9rem;
  }

  .circle-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .circle-info ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  .circle-info li {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.9rem;
  }

  .priority-demo {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .priority-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .priority-item h4 {
    margin: 0;
    font-size: 0.9rem;
    color: #495057;
    min-width: 200px;
  }
</style>
