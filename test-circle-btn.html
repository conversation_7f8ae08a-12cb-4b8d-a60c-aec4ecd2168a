<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圆形按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
            align-items: center;
        }
        .circle-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            background-color: #1890ff;
            color: white;
            border: 1px solid #1890ff;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .circle-btn:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        .circle-btn.outlined {
            background-color: transparent;
            color: #1890ff;
        }
        .circle-btn.outlined:hover {
            background-color: #f0f9ff;
            color: #40a9ff;
        }
        .circle-btn.text {
            background-color: transparent;
            color: #1890ff;
            border-color: transparent;
        }
        .circle-btn.text:hover {
            background-color: #f0f9ff;
        }
        .circle-btn.small {
            width: 24px;
            height: 24px;
            font-size: 12px;
        }
        .circle-btn.large {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }
        .priority-demo {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .priority-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .priority-item h4 {
            margin: 0;
            min-width: 200px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>⭕ 圆形按钮功能测试</h1>
    
    <div class="test-section">
        <h2>基础圆形按钮</h2>
        <div class="test-buttons">
            <button class="circle-btn">+</button>
            <button class="circle-btn outlined">🔍</button>
            <button class="circle-btn text">⚙️</button>
        </div>
    </div>

    <div class="test-section">
        <h2>不同尺寸</h2>
        <div class="test-buttons">
            <button class="circle-btn small">S</button>
            <button class="circle-btn">M</button>
            <button class="circle-btn large">L</button>
        </div>
    </div>

    <div class="test-section">
        <h2>内容优先级演示</h2>
        <div class="priority-demo">
            <div class="priority-item">
                <h4>1. 只有 icon 属性</h4>
                <button class="circle-btn">🏠</button>
                <span>显示 icon 属性的内容</span>
            </div>
            <div class="priority-item">
                <h4>2. icon 属性 + icon 插槽</h4>
                <button class="circle-btn">⭐</button>
                <span>显示 #icon 插槽的内容（优先级更高）</span>
            </div>
            <div class="priority-item">
                <h4>3. icon 属性 + icon 插槽 + 默认插槽</h4>
                <button class="circle-btn">A</button>
                <span>显示默认插槽的内容（优先级最高）</span>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>功能特性</h2>
        <ul>
            <li><strong>circle 属性</strong>: 设置按钮为圆形</li>
            <li><strong>icon 属性</strong>: 指定图标名称</li>
            <li><strong>#icon 插槽</strong>: 自定义图标内容</li>
            <li><strong>内容优先级</strong>: 默认插槽 > #icon 插槽 > icon 属性</li>
            <li><strong>尺寸适配</strong>: 自动调整为正方形</li>
            <li><strong>变体支持</strong>: 支持所有按钮变体</li>
            <li><strong>状态支持</strong>: 支持禁用、加载等状态</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>使用示例</h2>
        <pre><code><!-- 基础用法 -->
&lt;sp-btn circle icon="Add" /&gt;

<!-- 使用插槽 -->
&lt;sp-btn circle&gt;
  &lt;template #icon&gt;⭐&lt;/template&gt;
&lt;/sp-btn&gt;

<!-- 文字覆盖图标 -->
&lt;sp-btn circle icon="Home"&gt;A&lt;/sp-btn&gt;

<!-- 不同变体和尺寸 -->
&lt;sp-btn circle variant="outlined" size="large" icon="Search" /&gt;</code></pre>
    </div>

    <script>
        // 添加点击事件
        document.querySelectorAll('.circle-btn').forEach(button => {
            button.addEventListener('click', function() {
                alert(`点击了圆形按钮: ${this.textContent}`);
            });
        });
    </script>
</body>
</html>
