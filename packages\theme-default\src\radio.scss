/**
 * SpRadio 单选按钮样式
 */

.sp-radio {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;

  &__input {
    margin: 0;
    cursor: pointer;

    // 设置单选按钮的基础样式
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #d9d9d9;
    border-radius: 50%;
    background-color: #ffffff;
    position: relative;
    transition: all 0.2s ease, box-shadow 0.2s ease; /* 添加 box-shadow 过渡 */

    // 悬停效果
    &:hover {
      border-color: var(--sp-color-primary, #1890ff);
      box-shadow: 0 0 0 8px rgba(var(--sp-color-primary-rgb, 114, 46, 209), 0.1); /* 大阴影效果 */
    }

    // 选中状态
    &:checked {
      border-color: var(--sp-color-primary, #1890ff);
      background-color: #ffffff;

      // 选中状态的内部圆点
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--sp-color-primary, #1890ff);
        transform: translate(-50%, -50%);
      }

      // 选中状态的悬停效果
      &:hover {
        box-shadow: 0 0 0 8px
          rgba(var(--sp-color-primary-rgb, 114, 46, 209), 0.15); /* 选中状态的阴影稍微深一点 */
      }
    }

    // 禁用状态
    &:disabled {
      border-color: #d9d9d9;
      background-color: #f5f5f5;
      cursor: not-allowed;

      &:checked::after {
        background-color: #d9d9d9;
      }
    }
  }

  &__label {
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
  }

  // 尺寸变体
  &--small {
    .sp-radio__input {
      transform: scale(0.8);
    }

    .sp-radio__label {
      font-size: 12px;
    }
  }

  &--large {
    .sp-radio__input {
      transform: scale(1.2);
    }

    .sp-radio__label {
      font-size: 16px;
    }
  }

  // 状态变体
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .sp-radio__input,
    .sp-radio__label {
      cursor: not-allowed;
    }
  }

  &--checked {
    .sp-radio__label {
      color: var(--sp-color-primary, #1890ff);
    }
  }

  // 标签位置
  &--label-left {
    flex-direction: row-reverse;
  }
}

// 单选按钮组样式
.sp-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }

  &--inline {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }
}
