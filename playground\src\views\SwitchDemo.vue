<template>
  <div class="switch-demo">
    <h1>🔄 SpSwitch 开关组件演示</h1>

    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>🎯 基础用法</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="basicValue1"
          label="选项 1"
        />
        <SpSwitch
          v-model="basicValue2"
          label="选项 2"
        />
        <SpSwitch
          v-model="basicValue3"
          label="选项 3"
        />
      </div>
      <p class="demo-info">
        当前状态: {{ { basicValue1, basicValue2, basicValue3 } }}
      </p>
    </section>

    <!-- 尺寸变体 -->
    <section class="demo-section">
      <h2>📏 尺寸变体</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="sizeValue"
          size="small"
          label="小尺寸"
        />
        <SpSwitch
          v-model="sizeValue"
          size="medium"
          label="中等尺寸"
        />
        <SpSwitch
          v-model="sizeValue"
          size="large"
          label="大尺寸"
        />
      </div>
      <p class="demo-info">当前状态: {{ sizeValue }}</p>
    </section>

    <!-- 标签位置 -->
    <section class="demo-section">
      <h2>🏷️ 标签位置</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="labelValue"
          label="标签在右侧（默认）"
          label-position="right"
        />
        <SpSwitch
          v-model="labelValue"
          label="标签在左侧"
          label-position="left"
        />
      </div>
      <p class="demo-info">当前状态: {{ labelValue }}</p>
    </section>

    <!-- 变体样式 -->
    <section class="demo-section">
      <h2>🎨 变体样式</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="variantValue1"
          label="默认变体"
          variant="default"
        />
        <SpSwitch
          v-model="variantValue2"
          label="超大圆圈变体"
          variant="oversized"
        />
        <SpSwitch
          v-model="variantValue3"
          label="小尺寸超大圆圈"
          variant="oversized"
          size="small"
        />
        <SpSwitch
          v-model="variantValue4"
          label="大尺寸超大圆圈"
          variant="oversized"
          size="large"
        />
      </div>
      <p class="demo-info">
        当前状态:
        {{ { variantValue1, variantValue2, variantValue3, variantValue4 } }}
      </p>
    </section>

    <!-- 带内部文本 -->
    <section class="demo-section">
      <h2>📝 带内部文本</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="textValue1"
          label="中文文本"
          show-text
          checked-text="开"
          unchecked-text="关"
        />
        <SpSwitch
          v-model="textValue2"
          label="英文文本"
          show-text
          checked-text="ON"
          unchecked-text="OFF"
        />
        <SpSwitch
          v-model="textValue3"
          label="数字文本"
          show-text
          checked-text="1"
          unchecked-text="0"
          size="large"
        />
      </div>
      <p class="demo-info">
        当前状态: {{ { textValue1, textValue2, textValue3 } }}
      </p>
    </section>

    <!-- 状态演示 -->
    <section class="demo-section">
      <h2>🔒 状态演示</h2>
      <div class="demo-row">
        <div class="state-group">
          <h3>正常状态</h3>
          <SpSwitch
            v-model="stateValue1"
            label="正常开关"
          />
        </div>

        <div class="state-group">
          <h3>禁用状态</h3>
          <SpSwitch
            v-model="stateValue2"
            label="禁用开关"
            disabled
          />
        </div>
      </div>
      <p class="demo-info">当前状态: {{ { stateValue1, stateValue2 } }}</p>
    </section>

    <!-- 无标签开关 -->
    <section class="demo-section">
      <h2>🎛️ 无标签开关</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="noLabelValue1"
          :show-label="false"
        />
        <SpSwitch
          v-model="noLabelValue2"
          :show-label="false"
          size="small"
        />
        <SpSwitch
          v-model="noLabelValue3"
          :show-label="false"
          size="large"
        />
        <SpSwitch
          v-model="noLabelValue4"
          :show-label="false"
          show-text
          checked-text="✓"
          unchecked-text="✗"
        />
      </div>
      <p class="demo-info">
        当前状态:
        {{ { noLabelValue1, noLabelValue2, noLabelValue3, noLabelValue4 } }}
      </p>
    </section>

    <!-- 自定义颜色 -->
    <section class="demo-section">
      <h2>🎨 自定义颜色</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="colorValue1"
          label="默认主题色"
        />
        <SpSwitch
          v-model="colorValue2"
          label="自定义红色"
          color="#ff4d4f"
        />
        <SpSwitch
          v-model="colorValue3"
          label="自定义绿色"
          color="#52c41a"
        />
        <SpSwitch
          v-model="colorValue4"
          label="自定义橙色"
          color="#fa8c16"
        />
      </div>
      <p class="demo-info">
        当前状态: {{ { colorValue1, colorValue2, colorValue3, colorValue4 } }}
      </p>
    </section>

    <!-- 组合演示 -->
    <section class="demo-section">
      <h2>🎯 组合演示</h2>
      <div class="demo-row">
        <SpSwitch
          v-model="comboValue1"
          label="小尺寸 + 左标签 + 文本"
          size="small"
          label-position="left"
          show-text
          checked-text="是"
          unchecked-text="否"
        />
        <SpSwitch
          v-model="comboValue2"
          label="大尺寸 + 自定义颜色 + 文本"
          size="large"
          color="#722ed1"
          show-text
          checked-text="启用"
          unchecked-text="禁用"
        />
      </div>
      <p class="demo-info">当前状态: {{ { comboValue1, comboValue2 } }}</p>
    </section>

    <!-- 返回首页 -->
    <div class="demo-actions">
      <router-link
        to="/"
        class="back-link"
      >
        ← 返回首页
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 基础用法
  const basicValue1 = ref(false)
  const basicValue2 = ref(true)
  const basicValue3 = ref(false)

  // 尺寸变体
  const sizeValue = ref(false)

  // 标签位置
  const labelValue = ref(false)

  // 变体样式
  const variantValue1 = ref(false)
  const variantValue2 = ref(true)
  const variantValue3 = ref(false)
  const variantValue4 = ref(true)

  // 带内部文本
  const textValue1 = ref(false)
  const textValue2 = ref(true)
  const textValue3 = ref(false)

  // 状态演示
  const stateValue1 = ref(false)
  const stateValue2 = ref(true)

  // 无标签开关
  const noLabelValue1 = ref(false)
  const noLabelValue2 = ref(true)
  const noLabelValue3 = ref(false)
  const noLabelValue4 = ref(false)

  // 自定义颜色
  const colorValue1 = ref(false)
  const colorValue2 = ref(true)
  const colorValue3 = ref(false)
  const colorValue4 = ref(true)

  // 组合演示
  const comboValue1 = ref(false)
  const comboValue2 = ref(true)
</script>

<style scoped>
  .switch-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .demo-section {
    margin-bottom: 3rem;
    padding: 2rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-section h2 {
    margin: 0 0 1.5rem 0;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .demo-row {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    margin-bottom: 1rem;
  }

  .state-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .state-group h3 {
    margin: 0;
    font-size: 1rem;
    color: #666;
  }

  .demo-info {
    margin: 1rem 0 0 0;
    padding: 1rem;
    background: #f0f2f5;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #666;
  }

  .demo-actions {
    text-align: center;
    margin-top: 3rem;
  }

  .back-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--sp-color-primary, #667eea);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .back-link:hover {
    background: var(--sp-color-primary-hover, #7c8aeb);
    transform: translateY(-2px);
  }
</style>
