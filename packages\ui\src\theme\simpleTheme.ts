/**
 * 简化的主题设置系统
 * 用户只需要设置一个主题色，系统自动生成所有相关颜色
 */

// 颜色工具函数
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

function rgbToHex(r: number, g: number, b: number): string {
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

function lightenColor(color: string, amount: number): string {
  const rgb = hexToRgb(color)
  if (!rgb) return color

  const { r, g, b } = rgb
  const newR = Math.min(255, Math.round(r + ((255 - r) * amount) / 100))
  const newG = Math.min(255, Math.round(g + ((255 - g) * amount) / 100))
  const newB = Math.min(255, Math.round(b + ((255 - b) * amount) / 100))

  return rgbToHex(newR, newG, newB)
}

function darkenColor(color: string, amount: number): string {
  const rgb = hexToRgb(color)
  if (!rgb) return color

  const { r, g, b } = rgb
  const newR = Math.max(0, Math.round(r * (1 - amount / 100)))
  const newG = Math.max(0, Math.round(g * (1 - amount / 100)))
  const newB = Math.max(0, Math.round(b * (1 - amount / 100)))

  return rgbToHex(newR, newG, newB)
}

// 根据主题色生成完整的颜色系统
function generateThemeColors(primaryColor: string) {
  return {
    primary: primaryColor,
    primaryHover: lightenColor(primaryColor, 15), // 从 10% 调整到 15%，让 hover 效果更浅
    primaryActive: darkenColor(primaryColor, 10),
    primaryDisabled: lightenColor(primaryColor, 40),
    primaryLightest: lightenColor(primaryColor, 90), // 从 45% 调整到 50%，让最浅色更浅
    primaryLight: lightenColor(primaryColor, 90), // 从 30% 调整到 35%，让浅色更浅
    primaryDark: darkenColor(primaryColor, 20),
  }
}

// 提取 RGB 值（用于 rgba 函数）
function getRgbValues(color: string): string {
  const rgb = hexToRgb(color)
  if (!rgb) return '0, 0, 0'
  return `${rgb.r}, ${rgb.g}, ${rgb.b}`
}

// 更新 CSS 变量
function updateCSSVariables(colors: ReturnType<typeof generateThemeColors>) {
  if (typeof document === 'undefined') return

  const root = document.documentElement

  // 更新主题色相关的 CSS 变量
  root.style.setProperty('--sp-color-primary', colors.primary)
  root.style.setProperty('--sp-color-primary-hover', colors.primaryHover)
  root.style.setProperty('--sp-color-primary-active', colors.primaryActive)
  root.style.setProperty('--sp-color-primary-disabled', colors.primaryDisabled)
  root.style.setProperty('--sp-color-primary-lightest', colors.primaryLightest)
  root.style.setProperty('--sp-color-primary-light', colors.primaryLight)
  root.style.setProperty('--sp-color-primary-dark', colors.primaryDark)

  // 添加 RGB 值（用于 rgba 函数）
  root.style.setProperty('--sp-color-primary-rgb', getRgbValues(colors.primary))
}

// 当前主题色
let currentTheme = '#1890ff' // 默认蓝色

/**
 * 设置主题色
 * @param color 十六进制颜色值，如 '#1890ff'
 */
export function setTheme(color: string) {
  // 验证颜色格式
  if (!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {
    console.warn(
      `Invalid color format: ${color}. Please use hex format like #1890ff`
    )
    return
  }

  // 如果是3位hex，转换为6位
  if (color.length === 4) {
    color =
      '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3]
  }

  currentTheme = color
  const colors = generateThemeColors(color)
  updateCSSVariables(colors)
}

/**
 * 获取当前主题色
 */
export function getCurrentTheme(): string {
  return currentTheme
}

/**
 * 重置为默认主题
 */
export function resetTheme() {
  setTheme('#1890ff')
}

// 预设主题色
export const PRESET_THEMES = {
  blue: '#1890ff',
  green: '#52c41a',
  red: '#ff4d4f',
  orange: '#fa8c16',
  purple: '#722ed1',
  pink: '#eb2f96',
  cyan: '#13c2c2',
  gray: '#595959',
}

/**
 * 设置预设主题
 * @param themeName 预设主题名称
 */
export function setPresetTheme(themeName: keyof typeof PRESET_THEMES) {
  const color = PRESET_THEMES[themeName]
  if (color) {
    setTheme(color)
  } else {
    console.warn(`Unknown preset theme: ${themeName}`)
  }
}

// 初始化默认主题
if (typeof document !== 'undefined') {
  setTheme(currentTheme)
}
