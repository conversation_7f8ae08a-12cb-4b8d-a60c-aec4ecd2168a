<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Btn Rounded 属性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        .test-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            background-color: #1890ff;
            color: white;
            border: 1px solid #1890ff;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .test-button:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        .rounded-0 { border-radius: 0px; }
        .rounded-1 { border-radius: 4px; }
        .rounded-2 { border-radius: 8px; }
        .rounded-3 { border-radius: 12px; }
        .rounded-4 { border-radius: 16px; }
        .rounded-5 { border-radius: 20px; }
        .rounded-6 { border-radius: 24px; }
        .rounded-7 { border-radius: 28px; }
        .rounded-8 { border-radius: 32px; }
        .rounded-9 { border-radius: 36px; }
        .rounded-10 { border-radius: 40px; }
    </style>
</head>
<body>
    <h1>🔘 Btn Rounded 属性测试</h1>
    
    <div class="test-section">
        <h2>圆角数值演示 (0-10)</h2>
        <div class="test-buttons">
            <button class="test-button rounded-0">Rounded 0</button>
            <button class="test-button rounded-1">Rounded 1</button>
            <button class="test-button rounded-2">Rounded 2</button>
            <button class="test-button rounded-3">Rounded 3</button>
            <button class="test-button rounded-4">Rounded 4</button>
            <button class="test-button rounded-5">Rounded 5</button>
            <button class="test-button rounded-6">Rounded 6</button>
            <button class="test-button rounded-7">Rounded 7</button>
            <button class="test-button rounded-8">Rounded 8</button>
            <button class="test-button rounded-9">Rounded 9</button>
            <button class="test-button rounded-10">Rounded 10</button>
        </div>
    </div>

    <div class="test-section">
        <h2>圆角计算公式</h2>
        <p>圆角值 = rounded 属性值 × 4px</p>
        <ul>
            <li>rounded="0" → border-radius: 0px (完全方形)</li>
            <li>rounded="1" → border-radius: 4px</li>
            <li>rounded="2" → border-radius: 8px</li>
            <li>rounded="3" → border-radius: 12px</li>
            <li>rounded="4" → border-radius: 16px</li>
            <li>rounded="5" → border-radius: 20px (默认推荐)</li>
            <li>rounded="6" → border-radius: 24px</li>
            <li>rounded="7" → border-radius: 28px</li>
            <li>rounded="8" → border-radius: 32px</li>
            <li>rounded="9" → border-radius: 36px</li>
            <li>rounded="10" → border-radius: 40px (胶囊形状)</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>使用建议</h2>
        <ul>
            <li><strong>0-2</strong>: 适合现代、简洁的设计风格</li>
            <li><strong>3-5</strong>: 平衡的圆角，适合大多数场景</li>
            <li><strong>6-8</strong>: 较为柔和，适合友好的界面</li>
            <li><strong>9-10</strong>: 胶囊形状，适合特殊的设计需求</li>
        </ul>
    </div>

    <script>
        // 添加点击事件来显示当前圆角值
        document.querySelectorAll('.test-button').forEach(button => {
            button.addEventListener('click', function() {
                const className = this.className.match(/rounded-(\d+)/);
                if (className) {
                    const roundedValue = className[1];
                    const borderRadius = roundedValue * 4;
                    alert(`Rounded 值: ${roundedValue}\nCSS border-radius: ${borderRadius}px`);
                }
            });
        });
    </script>
</body>
</html>
