/**
 * SpControl 通用控件系统类型定义
 */

import type { Component, ComputedRef, Ref } from 'vue'

// 控件尺寸类型
export type ControlSize = 'small' | 'medium' | 'large'

// 控件变体类型
export type ControlVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error'

// 控件状态接口
export interface ControlState {
  /** 控件值 */
  value: any
  /** 是否禁用 */
  disabled: boolean
  /** 是否只读 */
  readonly: boolean
  /** 是否加载中 */
  loading: boolean
  /** 是否聚焦 */
  focused: boolean
  /** 是否悬停 */
  hovered: boolean
  /** 是否按下 */
  pressed: boolean
  /** 是否错误状态 */
  error: boolean
  /** 控件尺寸 */
  size: ControlSize
  /** 控件变体 */
  variant: ControlVariant
}

// 控件样式接口
export interface ControlStyles {
  /** CSS 类名数组 */
  classes: ComputedRef<(string | Record<string, boolean>)[]>
  /** 内联样式对象 */
  styles: ComputedRef<Record<string, string>>
}

// 控件行为接口
export interface ControlBehavior {
  /** 聚焦事件处理器 */
  handleFocus: (e: FocusEvent) => void
  /** 失焦事件处理器 */
  handleBlur: (e: FocusEvent) => void
  /** 鼠标进入事件处理器 */
  handleMouseenter: () => void
  /** 鼠标离开事件处理器 */
  handleMouseleave: () => void
  /** 鼠标按下事件处理器 */
  handleMousedown: () => void
  /** 鼠标释放事件处理器 */
  handleMouseup: () => void
  /** 键盘按下事件处理器 */
  handleKeydown: (e: KeyboardEvent) => void
  /** 更新值方法 */
  updateValue: (newValue: any) => void
}

// 控件上下文接口
export interface ControlContext {
  /** 控件状态 */
  state: ControlState
  /** 控件行为 */
  behavior: ControlBehavior
  /** 控件样式 */
  styles: ControlStyles
  /** 组件属性 */
  props: Record<string, any>
  /** 组件属性（透传） */
  attrs: Record<string, any>
}

// 控件事件接口
export interface ControlEmits {
  /** 模型值更新事件 */
  'update:modelValue': [value: any]
  /** 值变更事件 */
  'change': [value: any]
  /** 聚焦事件 */
  'focus': [e: FocusEvent]
  /** 失焦事件 */
  'blur': [e: FocusEvent]
  /** 点击事件 */
  'click': [e: MouseEvent]
  /** 鼠标进入事件 */
  'mouseenter': []
  /** 鼠标离开事件 */
  'mouseleave': []
  /** 鼠标按下事件 */
  'mousedown': []
  /** 鼠标释放事件 */
  'mouseup': []
  /** 键盘按下事件 */
  'keydown': [e: KeyboardEvent]
}

// 控件属性接口
export interface ControlProps {
  /** 模型值 */
  modelValue?: any
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 控件尺寸 */
  size?: ControlSize
  /** 控件变体 */
  variant?: ControlVariant
  /** 自定义颜色 */
  color?: string
  /** 是否错误状态 */
  error?: boolean
  /** 错误信息 */
  errorMessage?: string
  /** 圆角大小 (0-10) */
  rounded?: number
  /** Tab 索引 */
  tabindex?: string | number
  /** 无障碍标签 */
  ariaLabel?: string
  /** 无障碍描述 */
  ariaDescribedby?: string
  /** 角色 */
  role?: string
}

// 控件插槽接口
export interface ControlSlots {
  /** 默认插槽 */
  default?: (context: ControlContext) => any
  /** 前置插槽 */
  prepend?: (context: ControlContext) => any
  /** 后置插槽 */
  append?: (context: ControlContext) => any
  /** 图标插槽 */
  icon?: (context: ControlContext) => any
  /** 加载插槽 */
  loading?: (context: ControlContext) => any
}

// 控件配置接口
export interface ControlConfig {
  /** 控件名称 */
  name: string
  /** 控件组件 */
  component: Component
  /** 默认属性 */
  defaultProps?: Partial<ControlProps>
  /** 默认插槽 */
  defaultSlots?: Partial<ControlSlots>
}

// 控件注册表接口
export interface ControlRegistry {
  /** 注册控件 */
  register(config: ControlConfig): void
  /** 注销控件 */
  unregister(name: string): void
  /** 获取控件配置 */
  get(name: string): ControlConfig | undefined
  /** 获取所有控件配置 */
  getAll(): ControlConfig[]
  /** 检查控件是否已注册 */
  has(name: string): boolean
}

// 控件实例接口
export interface ControlInstance {
  /** 控件元素引用 */
  $el: HTMLElement
  /** 控件状态 */
  state: ControlState
  /** 聚焦控件 */
  focus(): void
  /** 失焦控件 */
  blur(): void
  /** 更新值 */
  updateValue(value: any): void
}

// 控件组合式函数返回类型
export interface UseControlReturn {
  /** 控件状态 */
  state: ControlState
  /** 控件行为 */
  behavior: ControlBehavior
  /** 控件样式 */
  styles: ControlStyles
}

// 控件工厂函数类型
export type ControlFactory<T extends ControlProps = ControlProps> = (
  props: T
) => UseControlReturn

// 控件高阶组件类型
export type ControlHOC<T extends ControlProps = ControlProps> = (
  component: Component
) => Component

// 导出所有类型
export type {
  ControlSize,
  ControlVariant,
  ControlState,
  ControlStyles,
  ControlBehavior,
  ControlContext,
  ControlEmits,
  ControlProps,
  ControlSlots,
  ControlConfig,
  ControlRegistry,
  ControlInstance,
  UseControlReturn,
  ControlFactory,
  ControlHOC,
}
