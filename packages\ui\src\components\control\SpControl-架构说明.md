# SpControl 通用控件系统架构说明

## 🎯 组件概述

`SpControl` 是 Speed UI 中所有交互控件的通用基础组件，它为 `SpButton`、`SpSwitch`、`SpSlider`、`SpRate` 等组件提供统一的交互逻辑、状态管理和事件处理机制。

## 🏗️ 设计模式

### 1. 通用控件模式 (Universal Control Pattern)
- **SpControl** 作为基础控件，提供通用的交互功能
- **SpButton**、**SpSwitch**、**SpSlider** 等作为具体实现，继承基础功能并添加特定行为
- **统一接口** 所有控件遵循相同的 API 设计规范

### 2. 状态驱动模式 (State-Driven Pattern)
- **响应式状态** 基于 Vue 3 响应式系统管理控件状态
- **状态计算** 通过 computed 自动计算派生状态
- **状态同步** 支持双向绑定和外部状态同步

### 3. 事件委托模式 (Event Delegation Pattern)
- **统一事件处理** 所有控件事件通过统一的处理器分发
- **事件冒泡** 支持事件冒泡和阻止冒泡
- **自定义事件** 支持自定义事件的注册和触发

## 🔧 核心功能

### 1. 通用状态管理
```typescript
// 控件基础状态接口
interface ControlState {
  value: any                    // 控件值
  disabled: boolean             // 禁用状态
  readonly: boolean             // 只读状态
  loading: boolean              // 加载状态
  focused: boolean              // 焦点状态
  hovered: boolean              // 悬停状态
  pressed: boolean              // 按下状态
  error: boolean                // 错误状态
  size: 'small' | 'medium' | 'large'  // 尺寸
  variant: string               // 变体
}
```

### 2. 交互行为管理
```typescript
// 统一的交互行为处理
const useControlBehavior = (props, emit) => {
  const state = reactive<ControlState>({...})
  
  // 焦点管理
  const handleFocus = (e: FocusEvent) => {
    if (state.disabled || state.readonly) return
    state.focused = true
    emit('focus', e)
  }
  
  // 值变更管理
  const updateValue = (newValue: any) => {
    if (state.disabled || state.readonly) return
    state.value = newValue
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
  
  return { state, handleFocus, updateValue }
}
```

### 3. 样式计算系统
```typescript
// 动态样式计算
const useControlStyles = (state, props) => {
  const classes = computed(() => [
    'sp-control',
    `sp-control--${props.variant}`,
    `sp-control--${state.size}`,
    {
      'sp-control--disabled': state.disabled,
      'sp-control--readonly': state.readonly,
      'sp-control--loading': state.loading,
      'sp-control--focused': state.focused,
      'sp-control--hovered': state.hovered,
      'sp-control--pressed': state.pressed,
      'sp-control--error': state.error,
    }
  ])
  
  const styles = computed(() => ({
    '--control-size': getSizeValue(state.size),
    '--control-color': getColorValue(props.color),
    '--control-radius': getRadiusValue(props.rounded),
  }))
  
  return { classes, styles }
}
```

## 🎨 控件系统架构

### 1. 核心组件层次
```
SpControl (基础控件)
├── useControlState (状态管理)
├── useControlBehavior (行为管理)
├── useControlStyles (样式管理)
├── useControlValidation (验证管理)
└── useControlAccessibility (无障碍管理)

具体控件实现:
├── SpButton (按钮控件)
├── SpSwitch (开关控件)
├── SpSlider (滑块控件)
├── SpRate (评分控件)
├── SpProgress (进度控件)
└── SpBadge (徽章控件)
```

### 2. 控件生命周期
```
1. 初始化阶段
   ├── 状态初始化
   ├── 事件绑定
   ├── 样式计算
   └── 无障碍设置

2. 交互阶段
   ├── 用户输入
   ├── 状态更新
   ├── 事件触发
   └── 样式重计算

3. 更新阶段
   ├── Props 变更
   ├── 状态同步
   ├── 重新渲染
   └── 副作用执行

4. 销毁阶段
   ├── 事件解绑
   ├── 状态清理
   └── 资源释放
```

## 📋 具体控件实现示例

### 1. SpButton (按钮控件)
```typescript
// SpButton 基于 SpControl 的实现
export const SpButton = defineComponent({
  name: 'SpButton',
  extends: SpControl,
  
  props: {
    ...makeControlProps(),
    type: {
      type: String as PropType<'button' | 'submit' | 'reset'>,
      default: 'button'
    },
    icon: String,
    iconPosition: {
      type: String as PropType<'left' | 'right'>,
      default: 'left'
    }
  },
  
  setup(props, { emit, slots }) {
    const { state, behavior, styles } = useControl(props, emit)
    
    // 按钮特有的点击处理
    const handleClick = (e: MouseEvent) => {
      if (state.disabled || state.loading) return
      
      behavior.handlePress()
      emit('click', e)
    }
    
    return () => (
      <button
        class={styles.classes.value}
        style={styles.styles.value}
        type={props.type}
        disabled={state.disabled}
        onClick={handleClick}
        onFocus={behavior.handleFocus}
        onBlur={behavior.handleBlur}
      >
        {props.icon && props.iconPosition === 'left' && (
          <SpIcon name={props.icon} />
        )}
        {slots.default?.()}
        {props.icon && props.iconPosition === 'right' && (
          <SpIcon name={props.icon} />
        )}
        {state.loading && <SpSpinner />}
      </button>
    )
  }
})
```

### 2. SpSwitch (开关控件)
```typescript
// SpSwitch 基于 SpControl 的实现
export const SpSwitch = defineComponent({
  name: 'SpSwitch',
  extends: SpControl,
  
  props: {
    ...makeControlProps(),
    checkedValue: { default: true },
    uncheckedValue: { default: false },
    checkedColor: String,
    uncheckedColor: String,
  },
  
  setup(props, { emit }) {
    const { state, behavior, styles } = useControl(props, emit)
    
    // 开关特有的切换逻辑
    const isChecked = computed(() => 
      state.value === props.checkedValue
    )
    
    const handleToggle = () => {
      if (state.disabled || state.readonly) return
      
      const newValue = isChecked.value 
        ? props.uncheckedValue 
        : props.checkedValue
        
      behavior.updateValue(newValue)
    }
    
    return () => (
      <div
        class={[
          styles.classes.value,
          'sp-switch',
          { 'sp-switch--checked': isChecked.value }
        ]}
        style={styles.styles.value}
        onClick={handleToggle}
        onKeydown={behavior.handleKeydown}
        tabindex={state.disabled ? -1 : 0}
        role="switch"
        aria-checked={isChecked.value}
      >
        <div class="sp-switch__track">
          <div class="sp-switch__thumb" />
        </div>
        {slots.default?.()}
      </div>
    )
  }
})
```

## 🔄 与 VSelectionControl 的对比

### 相似之处
| 特性 | VSelectionControl | SpControl |
|------|------------------|-----------|
| **基础组件模式** | ✅ 为选择控件提供基础 | ✅ 为所有控件提供基础 |
| **状态管理** | ✅ 统一的状态计算 | ✅ 统一的状态管理 |
| **事件处理** | ✅ 统一的事件机制 | ✅ 统一的事件系统 |
| **样式系统** | ✅ 动态样式计算 | ✅ 响应式样式系统 |
| **可扩展性** | ✅ 易于扩展新控件 | ✅ 易于创建新控件 |

### 架构差异
| 方面 | VSelectionControl | SpControl |
|------|------------------|-----------|
| **适用范围** | 选择类控件 | 所有交互控件 |
| **扩展方式** | 继承 + 插槽 | 组合 + Composables |
| **状态复杂度** | 相对简单 | 更加复杂 |
| **交互类型** | 主要是选择 | 多种交互类型 |
| **渲染方式** | 固定结构 | 灵活结构 |

## 🚀 使用示例

### 1. 基础使用
```vue
<template>
  <!-- 按钮控件 -->
  <SpButton 
    v-model="buttonState"
    variant="primary"
    size="medium"
    :loading="isLoading"
    @click="handleClick"
  >
    点击我
  </SpButton>
  
  <!-- 开关控件 -->
  <SpSwitch
    v-model="switchValue"
    checked-color="success"
    unchecked-color="gray"
    @change="handleSwitchChange"
  />
  
  <!-- 滑块控件 -->
  <SpSlider
    v-model="sliderValue"
    :min="0"
    :max="100"
    :step="1"
    show-tooltip
    @change="handleSliderChange"
  />
</template>
```

这种架构设计使得 Speed UI 能够提供功能强大、高度一致且易于扩展的通用控件系统！
