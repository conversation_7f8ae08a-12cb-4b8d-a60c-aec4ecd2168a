/**
 * SpSwitch 开关样式
 */

.sp-switch {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;

  &__input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    pointer-events: none;
  }

  // 隐藏 SpControl 包装器的样式影响
  .sp-control {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    pointer-events: none;
    min-height: 0;
    padding: 0;
    border: none;
    background: none;
  }

  &__wrapper {
    position: relative;
    display: inline-block;
  }

  &__track {
    position: relative;
    width: 44px;
    height: 22px;
    background-color: #d9d9d9;
    border-radius: 11px;
    transition: all 0.2s ease;
    cursor: pointer;

    // 悬停效果
    &:hover {
      box-shadow: 0 0 0 8px
        var(--sp-color-primary-lightest, rgba(24, 144, 255, 0.1));
    }
  }

  &__thumb {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: all 0.2s ease;
    transform: translateX(0);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &__text {
    font-size: 10px;
    font-weight: 500;
    color: #666;
    white-space: nowrap;
  }

  &__label {
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
  }

  // 选中状态
  &--checked {
    .sp-switch__track {
      background-color: var(--sp-color-primary, #1890ff);

      &:hover {
        background-color: var(--sp-color-primary-hover, #40a9ff);
        box-shadow: 0 0 0 8px
          var(--sp-color-primary-lightest, rgba(24, 144, 255, 0.15));
      }
    }

    .sp-switch__thumb {
      transform: translateX(22px); // 44px - 18px - 4px = 22px
    }

    .sp-switch__label {
      color: var(--sp-color-primary, #1890ff);
    }
  }

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .sp-switch__track,
    .sp-switch__label {
      cursor: not-allowed;
    }

    .sp-switch__track:hover {
      box-shadow: none;
    }

    &.sp-switch--checked .sp-switch__track {
      background-color: #d9d9d9;

      &:hover {
        background-color: #d9d9d9;
      }
    }
  }

  // 变体样式
  &--oversized {
    .sp-switch__track {
      width: 44px;
      height: 18px;
      border-radius: 9px;
    }

    .sp-switch__thumb {
      width: 26px;
      height: 26px;
      top: -4px;
      left: -4px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    &.sp-switch--checked .sp-switch__thumb {
      transform: translateX(22px); // 44px - 26px + 8px = 26px
    }

    .sp-switch__text {
      font-size: 9px;
    }

    .sp-switch__label {
      font-size: 14px;
    }
  }

  // 尺寸变体
  &--small {
    .sp-switch__track {
      width: 36px;
      height: 18px;
      border-radius: 9px;
    }

    .sp-switch__thumb {
      width: 14px;
      height: 14px;
      top: 2px;
      left: 2px;
    }

    &.sp-switch--checked .sp-switch__thumb {
      transform: translateX(18px); // 36px - 14px - 4px = 18px
    }

    .sp-switch__text {
      font-size: 8px;
    }

    .sp-switch__label {
      font-size: 12px;
    }
  }

  &--large {
    .sp-switch__track {
      width: 52px;
      height: 26px;
      border-radius: 13px;
    }

    .sp-switch__thumb {
      width: 22px;
      height: 22px;
      top: 2px;
      left: 2px;
    }

    &.sp-switch--checked .sp-switch__thumb {
      transform: translateX(26px); // 52px - 22px - 4px = 26px
    }

    .sp-switch__text {
      font-size: 12px;
    }

    .sp-switch__label {
      font-size: 16px;
    }
  }

  // oversized 变体的尺寸组合
  &--small.sp-switch--oversized {
    .sp-switch__track {
      width: 36px;
      height: 14px;
      border-radius: 7px;
    }

    .sp-switch__thumb {
      width: 20px;
      height: 20px;
      top: -3px;
      left: -3px;
    }

    &.sp-switch--checked .sp-switch__thumb {
      transform: translateX(20px); // 36px - 20px + 4px = 20px
    }

    .sp-switch__text {
      font-size: 8px;
    }

    .sp-switch__label {
      font-size: 12px;
    }
  }

  &--large.sp-switch--oversized {
    .sp-switch__track {
      width: 52px;
      height: 22px;
      border-radius: 11px;
    }

    .sp-switch__thumb {
      width: 32px;
      height: 32px;
      top: -5px;
      left: -5px;
    }

    &.sp-switch--checked .sp-switch__thumb {
      transform: translateX(30px); // 52px - 32px + 10px = 30px
    }

    .sp-switch__text {
      font-size: 10px;
    }

    .sp-switch__label {
      font-size: 16px;
    }
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .sp-switch__track,
  .sp-switch__thumb {
    transition: none;
  }
}
