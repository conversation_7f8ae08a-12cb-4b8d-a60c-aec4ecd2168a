# VSelectionControl 组件架构说明

## 🎯 组件概述

`VSelectionControl` 是 Vuetify 中所有选择控件的基础组件，它为 `VRadio`、`VCheckbox`、`VSwitch` 等组件提供统一的底层实现。

## 🏗️ 设计模式

### 1. 基础组件模式 (Base Component Pattern)
- **VSelectionControl** 作为基础组件，提供通用功能
- **VRadio**、**VCheckbox**、**VSwitch** 作为具体实现，继承基础功能并添加特定行为

### 2. 组合模式 (Composition Pattern)
- 通过插槽系统允许子组件自定义渲染
- 支持 `default`、`label`、`input` 三种插槽
- 每个插槽都提供丰富的参数供自定义使用

### 3. Composable 模式
- `useSelectionControl` 函数封装所有业务逻辑
- 分离视图和逻辑，提高可测试性和复用性

## 🔧 核心功能

### 1. 状态管理
```typescript
// 支持单选和多选模式
const isMultiple = computed(() => 
  !!props.multiple || Array.isArray(modelValue.value)
)

// 智能的值比较和状态计算
const model = computed({
  get() {
    return isMultiple.value
      ? wrapInArray(val).some(v => props.valueComparator(v, trueValue.value))
      : props.valueComparator(val, trueValue.value)
  },
  set(val: boolean) {
    // 处理单选/多选的值更新逻辑
  }
})
```

### 2. 组合控件支持
```typescript
// 支持 RadioGroup、CheckboxGroup 等组合控件
const group = inject(VSelectionControlGroupSymbol, undefined)

// 优先使用组的值，否则使用本地值
const val = group ? group.modelValue.value : modelValue.value
```

### 3. 样式系统
```typescript
// 动态颜色计算
const { textColorClasses, textColorStyles } = useTextColor(() => {
  return model.value ? props.color : props.baseColor
})

// 密度支持
const { densityClasses } = useDensity(props)
```

## 🎨 插槽系统

### 1. default 插槽
用于自定义选择控件的外观，提供背景颜色相关的类名和样式。

### 2. label 插槽
用于自定义标签内容，提供标签文本和属性。

### 3. input 插槽
最重要的插槽，用于完全自定义输入控件的渲染，提供：
- `model`: 双向绑定的模型值
- `textColorClasses/Styles`: 文本颜色
- `backgroundColorClasses/Styles`: 背景颜色
- `inputNode`: 原生 input 元素
- `icon`: 当前状态对应的图标
- `props`: 事件处理器和 ID

## 📋 VRadio 的实现

VRadio 是 VSelectionControl 的一个简单包装器：

```typescript
export const VRadio = genericComponent<VSelectionControlSlots>()({
  setup(props, { slots }) {
    useRender(() => {
      const controlProps = VSelectionControl.filterProps(props)
      
      return (
        <VSelectionControl
          {...controlProps}
          class={['v-radio', props.class]}
          type="radio"
          v-slots={slots}
        />
      )
    })
  }
})
```

### VRadio 的特点：
1. **预设图标**: 自动设置 `$radioOff` 和 `$radioOn` 图标
2. **类型设置**: 设置 `type="radio"` 用于表单支持
3. **样式类**: 添加 `v-radio` CSS 类用于特定样式
4. **透传**: 完全透传所有属性和插槽给 VSelectionControl

## 🔄 工作流程

1. **初始化**: VRadio 创建时，设置预定义的图标和类型
2. **状态计算**: VSelectionControl 根据 modelValue 计算选中状态
3. **渲染**: 根据状态渲染对应的图标和样式
4. **交互**: 用户点击时，更新 modelValue 并触发重新渲染
5. **组合**: 如果在 RadioGroup 中，状态会同步到组级别

## 🎯 设计优势

### 1. 代码复用
- 所有选择控件共享相同的基础逻辑
- 减少重复代码，提高维护性

### 2. 一致性
- 统一的 API 设计
- 一致的行为模式
- 统一的样式系统

### 3. 扩展性
- 通过插槽系统支持高度自定义
- 新的选择控件可以轻松基于 VSelectionControl 构建

### 4. 类型安全
- 泛型支持提供完整的类型检查
- 插槽参数类型化，提供良好的开发体验

## 📚 使用示例

```vue
<!-- 基础用法 -->
<VRadio v-model="selected" value="option1" label="选项1" />

<!-- 自定义图标 -->
<VRadio v-model="selected" value="option2">
  <template #input="{ model, icon }">
    <CustomIcon :icon="icon" :checked="model" />
  </template>
  自定义选项
</VRadio>

<!-- 在组中使用 -->
<VRadioGroup v-model="groupValue">
  <VRadio value="a" label="选项A" />
  <VRadio value="b" label="选项B" />
</VRadioGroup>
```

这种架构设计使得 Vuetify 能够提供功能强大、高度一致且易于扩展的选择控件系统。
